import passport from "@outlinewiki/koa-passport";
import bcrypt from "bcryptjs";
import { Strategy as LocalStrategy } from "passport-local";
import type { Context } from "koa";
import Router from "koa-router";
import { parseDomain } from "@shared/utils/domains";
import { AuthenticationError } from "@server/errors";
import { rateLimiter } from "@server/middlewares/rateLimiter";
import passportMiddleware from "@server/middlewares/passport";
import { User, Team } from "@server/models";
import { RateLimiterStrategy } from "@server/utils/RateLimiter";
import config from "../../plugin.json";

const router = new Router();

// 配置本地认证策略
if (config.id) {
  passport.use(
    config.id,
    new LocalStrategy(
      {
        usernameField: "username",
        passwordField: "password",
        passReqToCallback: true,
      },
      async (req: any, username: string, password: string, done) => {
        try {
          const domain = parseDomain(req.hostname);

          let team: Team | null | undefined;
          if (domain.custom) {
            team = await Team.findOne({
              where: { domain: domain.host },
            });
          } else if (domain.teamSubdomain) {
            team = await Team.findOne({
              where: { subdomain: domain.teamSubdomain },
            });
          } else {
            team = await Team.findOne({
              order: [["createdAt", "DESC"]],
            });
          }

          if (!team) {
            return done(new AuthenticationError("Team not found"));
          }

          // 查找用户（使用用户名作为邮箱字段存储）
          const user = await User.findOne({
            where: {
              teamId: team.id,
              email: username.toLowerCase(),
            },
          });

          if (!user) {
            return done(new AuthenticationError("Invalid credentials"));
          }

          // 验证密码（从passwordDigest字段）
          if (!user.passwordDigest) {
            return done(new AuthenticationError("Password not set"));
          }

          const isValidPassword = await bcrypt.compare(password, user.passwordDigest);
          if (!isValidPassword) {
            return done(new AuthenticationError("Invalid credentials"));
          }

          return done(null, { user, team, isNewTeam: false });
        } catch (error) {
          return done(error);
        }
      }
    )
  );

  // 本地登录路由
  router.post(
    config.id,
    rateLimiter(RateLimiterStrategy.TenPerHour),
    passportMiddleware(config.id)
  );
}

export default router;
