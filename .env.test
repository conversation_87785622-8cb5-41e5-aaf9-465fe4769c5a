NODE_ENV=test
DATABASE_URL=postgres://user:pass@127.0.0.1:5432/outline-test
SECRET_KEY=F0E5AD933D7F6FD8F4DBB3E038C501C052DC0593C686D21ACB30AE205D2F634B

SMTP_HOST=smtp.example.com
SMTP_USERNAME=test
SMTP_FROM_EMAIL=<EMAIL>
SMTP_REPLY_EMAIL=<EMAIL>

GOOGLE_CLIENT_ID=123
GOOGLE_CLIENT_SECRET=123

SLACK_CLIENT_ID=123
SLACK_CLIENT_SECRET=123

GITHUB_CLIENT_ID=123;
GITHUB_CLIENT_SECRET=123;
GITHUB_APP_NAME=outline-test;

OIDC_CLIENT_ID=client-id
OIDC_CLIENT_SECRET=client-secret
OIDC_AUTH_URI=http://localhost/authorize
OIDC_TOKEN_URI=http://localhost/token
OIDC_USERINFO_URI=http://localhost/userinfo

IFRAMELY_API_KEY=123

RATE_LIMITER_ENABLED=false

FILE_STORAGE=local
FILE_STORAGE_LOCAL_ROOT_DIR=/tmp
