{"New API key": "Yeni API Anahtarı", "Open collection": "Koleksiyonu Aç", "New collection": "<PERSON><PERSON> k<PERSON>", "Create a collection": "Bir koleksiyon oluştur", "Edit": "<PERSON><PERSON><PERSON><PERSON>", "Edit collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Permissions": "<PERSON><PERSON><PERSON>", "Collection permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Share this collection": "<PERSON><PERSON> koleks<PERSON><PERSON>", "Search in collection": "Koleksiyonda ara", "Star": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Unstar": "Yıldızı kaldır", "Subscribe": "<PERSON><PERSON>", "Subscribed to document notifications": "Belge bildiri<PERSON>ine abone o<PERSON>u", "Unsubscribe": "Abonelikten çık", "Unsubscribed from document notifications": "Belge bildirimleri aboneliğinden çıkıldı", "Archive": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Archive collection": "Arşiv koleksiyonu", "Collection archived": "Arşiv koleksiyonu", "Archiving": "Arşivleniyor", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "Bu koleksiyonu a<PERSON><PERSON>, içindeki tüm belgeleri de arşivleyecektir. Koleksiyondaki belgeler artık arama sonuçlarında görünmeyecek.", "Restore": "<PERSON><PERSON>", "Collection restored": "Koleksiyon geri <PERSON>", "Delete": "Sil", "Delete collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> sil", "New template": "<PERSON><PERSON>", "Delete comment": "<PERSON><PERSON><PERSON> sil", "Mark as resolved": "Çözüldü olarak işaretle", "Thread resolved": "Konuşma dizisi <PERSON>", "Mark as unresolved": "Çözülmemiş olarak işaretle", "View reactions": "Tepkileri görü<PERSON>üle", "Reactions": "<PERSON><PERSON><PERSON><PERSON>", "Copy ID": "<PERSON>li<PERSON><PERSON>", "Clear IndexedDB cache": "IndexedDB önbelleğini temizle", "IndexedDB cache cleared": "IndexedDB önbelleği temizlendi", "Toggle debug logging": "Hata ayıklama günlüklemeyi aç/kapat", "Debug logging enabled": "Hata ayıklama günlüğü etkinleştirildi", "Debug logging disabled": "Hata ayıklama günlüğü devre dışı bırakıldı", "Development": "G<PERSON>ştirme", "Open document": "Belgeyi aç", "New document": "<PERSON><PERSON> be<PERSON>", "New draft": "<PERSON><PERSON>", "New from template": "Şablondan yeni", "New nested document": "<PERSON><PERSON> iç içe belge", "Publish": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Published {{ documentName }}": "Yayımlandı {{ documentName }}", "Publish document": "Belgeyi Yayınla", "Unpublish": "<PERSON><PERSON><PERSON><PERSON><PERSON> kaldır", "Unpublished {{ documentName }}": "Yayımlanmadı {{ documentName }}", "Share this document": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "HTML": "HTML", "PDF": "PDF", "Exporting": "Dışa aktarılıyor", "Markdown": "<PERSON><PERSON>", "Download": "<PERSON><PERSON><PERSON>", "Download document": "Belgeyi indir", "Copy as Markdown": "<PERSON><PERSON> o<PERSON><PERSON>", "Markdown copied to clipboard": "Bağlantı panoya kopyalandı", "Copy as text": "<PERSON><PERSON> k<PERSON>", "Text copied to clipboard": "Text copied to clipboard", "Copy public link": "Genel bağlantıyı kopyala", "Link copied to clipboard": "Bağlantı panoya kopyalandı", "Copy link": "Bağlantıyı kopyala", "Copy": "Kopyala", "Duplicate": "Çoğalt", "Duplicate document": "Belgeyi çoğalt", "Copy document": "Belgeyi kopyala", "collection": "koleksiyon", "Pin to {{collectionName}}": "{{collectionName}} koleksiyonuna sabitle", "Pinned to collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Pin to home": "<PERSON><PERSON><PERSON><PERSON>", "Pinned to home": "<PERSON> <PERSON><PERSON><PERSON>", "Pin": "<PERSON><PERSON><PERSON>", "Search in document": "Belgelerde ara", "Print": "Yazdır", "Print document": "Belgeyi yazdır", "Import document": "Belgeyi içeri aktar", "Templatize": "Şablona dönüştür", "Create template": "Şablon oluştur", "Open random document": "<PERSON><PERSON><PERSON><PERSON> belge aç", "Search documents for \"{{searchQuery}}\"": "\"{{searchQuery}}\" için arama sonuçları", "Move to workspace": "Çalışma alanına taşı", "Move": "Taşı", "Move to collection": "Koleksiyona ta<PERSON>ı", "Move {{ documentType }}": "{{ documentType }} taşı", "Are you sure you want to archive this document?": "Bu belgeyi arşivlemek istediğinizden emin misiniz?", "Document archived": "Belge arşivlendi", "Archiving this document will remove it from the collection and search results.": "<PERSON><PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, koleksiyondan ve arama sonuçlarından kaldıracaktır.", "Delete {{ documentName }}": "{{ documentName }} sil", "Permanently delete": "Kalıcı olarak sil", "Permanently delete {{ documentName }}": "{{ documentName }} kalıcı olarak sil", "Empty trash": "Çöpü boşalt", "Permanently delete documents in trash": "Çöpteki belgeleri kalıcı olarak sil", "Comments": "<PERSON><PERSON><PERSON>", "History": "Geçmiş", "Insights": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Disable viewer insights": "Görüntüleyici içgörülerini devre dışı bırak", "Enable viewer insights": "İzleyici analizlerini etkinleştir", "Leave document": "Belgeden ayrıl", "You have left the shared document": "Paylaşılan belgeden ayrıldınız", "Could not leave document": "Could not leave document", "Home": "<PERSON><PERSON><PERSON>", "Drafts": "Taslaklar", "Search": "Ara", "Trash": "<PERSON><PERSON><PERSON>", "Settings": "<PERSON><PERSON><PERSON>", "Profile": "Profil", "Templates": "Şablonlar", "Notifications": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Preferences": "<PERSON><PERSON><PERSON><PERSON>", "Documentation": "Dokümantasyon", "API documentation": "API dokümantasyonu", "Toggle sidebar": "<PERSON>ar <PERSON> a<PERSON>/kapat", "Send us feedback": "<PERSON><PERSON> bi<PERSON><PERSON><PERSON>", "Report a bug": "<PERSON><PERSON> bildir", "Changelog": "Değişiklik Notları", "Keyboard shortcuts": "Klavye kısayolları", "Download {{ platform }} app": "{{ platform }} uygulamasını indir", "Log out": "<PERSON><PERSON><PERSON><PERSON> kapat", "Mark notifications as read": "Bildirimleri okundu olarak işaretle", "Archive all notifications": "<PERSON><PERSON><PERSON> bildirimleri arşivle", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "Bağlantı kopyalandı", "Dark": "<PERSON><PERSON>", "Light": "Açık", "System": "Sistem", "Appearance": "G<PERSON>rü<PERSON><PERSON><PERSON>", "Change theme": "Temayı değiştir", "Change theme to": "Temayı değiştir", "Switch workspace": "Çalışma Alanını Değiştir", "Select a workspace": "Bir çalışma alanı seçin", "New workspace": "<PERSON>ni <PERSON> alanı", "Create a workspace": "<PERSON>ni <PERSON> alanı oluştur", "Login to workspace": "Çalışma alanına giriş yap", "Invite people": "<PERSON><PERSON><PERSON><PERSON> et", "Invite to workspace": "Çalışma al<PERSON> da<PERSON> et", "Promote to {{ role }}": "{{ role }} r<PERSON><PERSON><PERSON> terfi ettir", "Demote to {{ role }}": "{{ role }} r<PERSON><PERSON><PERSON> terfi ettir", "Update role": "<PERSON><PERSON><PERSON>", "Delete user": "Kullanıcıyı sil", "Collection": "Koleksiyon", "Collections": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Debug": "<PERSON><PERSON>", "Document": "Belge", "Documents": "<PERSON><PERSON><PERSON>", "Recently viewed": "<PERSON> <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Revision": "Revizyon", "Navigation": "Navigasyon", "Notification": "<PERSON><PERSON><PERSON><PERSON>", "People": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Workspace": "Çalışma Alanı", "Recent searches": "<PERSON>", "currently editing": "<PERSON>u anda d<PERSON>r", "currently viewing": "<PERSON>u anda <PERSON><PERSON>", "previously edited": "önceden düzenlendi", "You": "<PERSON>", "Viewers": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Collections are used to group documents and choose permissions": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, belgeleri gruplayıp izinleri seçmek için k<PERSON>anılır", "Name": "Ad", "The default access for workspace members, you can share with more users or groups later.": "Çalışma alanı üyeleri için <PERSON> er<PERSON>, daha sonra daha fazla kullanıcı veya grup ile paylaşabilirsiniz.", "Public document sharing": "Herkese açık belge paylaşımı", "Allow documents within this collection to be shared publicly on the internet.": "Bu koleksiyondaki belgelerin internet üzerinde herkese açık şekilde <PERSON>laşılmasına izin ver.", "Commenting": "Commenting", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Save": "<PERSON><PERSON>", "Creating": "Oluşturuluyor", "Create": "Oluştur", "Collection deleted": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "I’m sure – Delete": "Eminim – Sil", "Deleting": "Silini<PERSON><PERSON>", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "Bundan emin misiniz? <em>{{collectionName}}</em> koleksiyonunu silmek kalıcıdır ve geri alınamaz, ancak içindeki tüm yayımlanan belgeler çöp kutusuna taşınacaktır.", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "<PERSON><PERSON><PERSON><PERSON>, ba<PERSON><PERSON><PERSON><PERSON> görün<PERSON><PERSON><PERSON> olarak <em>{{collectionName}}</em> kullanılıyor - <PERSON><PERSON><PERSON>i, ba<PERSON><PERSON><PERSON><PERSON> görünümünü Ana sayfaya sıfırlayacaktır.", "Type a command or search": "Bir komut yazın veya arayın", "Choose a template": "Bir şablon seç", "Are you sure you want to permanently delete this entire comment thread?": "Bu yorum dizisinin tamamını kalıcı olarak silmek istediğinizden emin misiniz?", "Are you sure you want to permanently delete this comment?": "Bu yorumu kalıcı olarak silmek istediğinizden emin misiniz?", "Confirm": "<PERSON><PERSON><PERSON>", "manage access": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "view and edit access": "<PERSON><PERSON>ş<PERSON><PERSON> görüntüleme ve düzenleme", "view only access": "yalnızca görüntüleme er<PERSON>imi", "no access": "<PERSON><PERSON><PERSON><PERSON> yok", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "{{ documentName }} be<PERSON><PERSON>i {{ collectionName }} koleksiyonuna taşıma izniniz yok", "Move document": "Belgeyi taşı", "Moving": "Taşınıyor", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "<em>{{ title }}</em> belgesini {{ newCollectionName }} koleksiyonuna taşımak, tüm çalışma alanı üyelerinin izinlerini <em>{{ prevPermission }}</em>'dan <em>{{ newPermission }}</em>'a değiştirecektir.", "Submenu": "<PERSON>", "Collections could not be loaded, please reload the app": "Koleksiyonlar yü<PERSON>medi, lütfen uygulamayı yeniden yükleyin", "Default collection": "Varsayılan koleksiyon", "Start view": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ba<PERSON><PERSON>", "Install now": "<PERSON><PERSON><PERSON>", "Deleted Collection": "Silinmiş Koleksiyon", "Untitled": "Başlıksız", "Unpin": "Sabitlemeyi kaldır", "{{ minutes }}m read": "{{ minutes }}da<PERSON><PERSON> okuma", "Select a location to copy": "Kopyalamak için bir konum seçin", "Document copied": "Belge kopyalandı", "Couldn’t copy the document, try again?": "<PERSON>ge kopyalanamadı, tekrar den<PERSON>?", "Include nested documents": "İç içe geçmiş belgeleri dahil et", "Copy to <em>{{ location }}</em>": "<em>{{ location }}</em>'ye kopyala", "Search collections & documents": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>a ve belgelerde arama yapın", "No results found": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "New": "<PERSON><PERSON>", "Only visible to you": "<PERSON><PERSON><PERSON> sana g<PERSON><PERSON><PERSON><PERSON>", "Draft": "Taslak", "Template": "Şablon", "You updated": "<PERSON><PERSON> gü<PERSON><PERSON><PERSON>", "{{ userName }} updated": "{{ userName }} g<PERSON><PERSON><PERSON>i", "You deleted": "<PERSON><PERSON>ld<PERSON>", "{{ userName }} deleted": "{{ userName }} sildi", "You archived": "Siz arşivlediniz", "{{ userName }} archived": "{{ userName }} arşivledi", "Imported": "İthal Edildi", "You created": "Siz oluşturdunuz", "{{ userName }} created": "{{ userName }} oluşturdu", "You published": "Siz yayınladınız", "{{ userName }} published": "{{ userName }} ya<PERSON>ınladı", "Never viewed": "<PERSON><PERSON>medi", "Viewed": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "in": "içinde", "nested document": "i<PERSON> içe belge", "nested document_plural": "i<PERSON> i<PERSON>e belgeler", "{{ total }} task": "{{ total }} görev", "{{ total }} task_plural": "{{ total }} görev", "{{ completed }} task done": "{{ completed }} g<PERSON><PERSON>v ta<PERSON>ı", "{{ completed }} task done_plural": "{{ completed }} g<PERSON><PERSON>v ta<PERSON>ı", "{{ completed }} of {{ total }} tasks": "Toplam {{ total }} görevden {{ completed }} kadarı tamamlandı", "Currently editing": "<PERSON><PERSON> anda <PERSON>", "Currently viewing": "<PERSON><PERSON> and<PERSON><PERSON>", "Viewed {{ timeAgo }}": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> {{ timeAgo }}", "Module failed to load": "<PERSON><PERSON><PERSON><PERSON>", "Loading Failed": "Yükleme başarısız oldu", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "Maalesef uygulamanın bir kısmı yüklenemedi. <PERSON><PERSON><PERSON>, sekmeyi açtığınızdan beri güncellenmiş olması veya başarısız bir ağ isteği olabilir. Lütfen yeniden yüklemeyi deneyin.", "Reload": "<PERSON><PERSON><PERSON>", "Something Unexpected Happened": "Beklenmedik Bir Şey Oldu", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kurtar<PERSON>lamaz bir hata oluştu{{notified}}. Lütfen sayfayı yeniden yüklemeyi den<PERSON>, geçici bir hata olabilir.", "our engineers have been notified": "mühendislerimiz bilgilendirildi", "Show detail": "Detayı göster", "Revision deleted": "Revision deleted", "Current version": "<PERSON><PERSON><PERSON> s<PERSON>", "{{userName}} edited": "{{userName}} g<PERSON><PERSON><PERSON>i", "{{userName}} archived": "{{userName}} arşivledi", "{{userName}} restored": "{{userName}} geri <PERSON>", "{{userName}} deleted": "{{userName}} sildi", "{{userName}} added {{addedUserName}}": "{{userName}} {{addedUserName}}'yi e<PERSON>i", "{{userName}} removed {{removedUserName}}": "{{userName}} removed {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} çöp kutusundan taşıdı", "{{userName}} published": "{{userName}} ya<PERSON>ınladı", "{{userName}} unpublished": "{{userName}} yayınlanmamış", "{{userName}} moved": "{{userName}} taşıdı", "Export started": "Dışa aktarma başladı", "Your file will be available in {{ location }} soon": "Dosyanız yakında {{ location }}'da kullanılabilir olacak", "View": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "A ZIP file containing the images, and documents in the Markdown format.": "Görselleri ve dokümanları Markdown biçiminde içeren bir ZIP dosyası.", "A ZIP file containing the images, and documents as HTML files.": "Görselleri ve dokümanları HTML dosyaları olarak içeren bir ZIP dosyası.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "Verileri başka bir uyumlu {{ appName }} uygulamasına aktarmak için kullanılabilecek yapılandırılmış veri.", "Export": "Dışa Aktar", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "<em>{{collectionName}}</em> koleksiyonunu dışa aktarmak biraz zaman alabilir.", "You will receive an email when it's complete.": "Tamamlandığında bir e-posta alacaksınız.", "Include attachments": "Eklentileri dahil et", "Including uploaded images and files in the exported data": "Yüklenen resimler ve dosyalar dahil olmak üzere verilerin dışa aktarılması", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "Filtre", "No results": "<PERSON><PERSON><PERSON> bulu<PERSON>adı", "{{authorName}} created <3></3>": "{{authorName}} created <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} opened <3></3>", "Search emoji": "Search emoji", "Search icons": "<PERSON><PERSON> si<PERSON>", "Choose default skin tone": "Choose default skin tone", "Show menu": "Men<PERSON>yü <PERSON>", "Icon Picker": "<PERSON><PERSON> Picker", "Icons": "<PERSON><PERSON><PERSON><PERSON>", "Emojis": "Emojis", "Remove": "Kaldır", "All": "All", "Frequently Used": "Frequently Used", "Search Results": "<PERSON>ma <PERSON>", "Smileys & People": "Smileys & People", "Animals & Nature": "Animals & Nature", "Food & Drink": "Food & Drink", "Activity": "Activity", "Travel & Places": "Travel & Places", "Objects": "Objects", "Symbols": "Symbols", "Flags": "Flags", "Select a color": "Renk seçiniz", "Loading": "Yükleniyor", "Permission": "Permission", "View only": "<PERSON><PERSON><PERSON>", "Can edit": "Can edit", "No access": "<PERSON><PERSON><PERSON><PERSON> yok", "Default access": "Varsayılan <PERSON>", "Change Language": "<PERSON><PERSON>", "Dismiss": "<PERSON><PERSON><PERSON>", "You’re offline.": "Çevrimdışısınız.", "Sorry, an error occurred.": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir hata o<PERSON>.", "Click to retry": "<PERSON><PERSON>r denemek için tı<PERSON>ın", "Back": "<PERSON><PERSON>", "Unknown": "Bilinmeyen", "Mark all as read": "Tümünü okundu olarak işaretle", "You're all caught up": "You're all caught up", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "Published", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} and {{ count }} other reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}", "Add reaction": "<PERSON><PERSON><PERSON>", "Reaction picker": "<PERSON><PERSON><PERSON>", "Could not load reactions": "<PERSON><PERSON><PERSON><PERSON> yükle<PERSON>medi", "Reaction": "<PERSON><PERSON><PERSON>", "Results": "Sonuçlar", "No results for {{query}}": "{{query}} i<PERSON><PERSON> son<PERSON> bulu<PERSON>ı", "Manage": "<PERSON><PERSON><PERSON>", "All members": "<PERSON><PERSON><PERSON>", "Everyone in the workspace": "Çalışma al<PERSON> herkes", "{{ count }} member": "{{ count }} üye", "{{ count }} member_plural": "{{ count }} <PERSON><PERSON><PERSON>", "Invite": "<PERSON><PERSON> et", "{{ userName }} was added to the collection": "{{ userName }} k<PERSON><PERSON><PERSON><PERSON><PERSON> e<PERSON>", "{{ count }} people added to the collection": "{{ count }} people added to the collection", "{{ count }} people added to the collection_plural": "{{ count }} people added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} people and {{ count2 }} groups added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} people and {{ count2 }} groups added to the collection", "Add": "<PERSON><PERSON>", "Add or invite": "<PERSON><PERSON> veya davet et", "Viewer": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Editor": "D<PERSON><PERSON>leyici", "Suggestions for invitation": "<PERSON><PERSON> <PERSON><PERSON>", "No matches": "Eşleşme yok", "Can view": "G<PERSON>r<PERSON><PERSON><PERSON>leyebilir", "Everyone in the collection": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "You have full access": "<PERSON> var", "Created the document": "", "Other people": "<PERSON><PERSON><PERSON>", "Other workspace members may have access": "<PERSON><PERSON><PERSON>ışma alanı üyeleri erişime sahip olabilir", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "<PERSON><PERSON> belge, erişiminizin olmadığı bir ana belge veya koleksiyon aracılığıyla daha fazla çalışma alanı üyesiyle <PERSON>şılabilir", "Access inherited from collection": "Koleksiyondan mi<PERSON> alı<PERSON>", "{{ userName }} was removed from the document": "{{ userName }} was removed from the document", "Could not remove user": "Kullanıcı kaldırılamadı", "Permissions for {{ userName }} updated": "Permissions for {{ userName }} updated", "Could not update user": "Kullanıcı güncellenemedi", "Has access through <2>parent</2>": "Has access through <2>parent</2>", "Suspended": "Ask<PERSON>ya alındı", "Invited": "<PERSON><PERSON> edildi", "Active <1></1> ago": "<1></1> önce aktif", "Never signed in": "Daha önce hiç oturum açmadı", "Leave": "Ayrıl", "Only lowercase letters, digits and dashes allowed": "Yalnızca küçük harflere, rakamlara ve kısa çizgilere izin verilir", "Sorry, this link has already been used": "Üzg<PERSON><PERSON><PERSON><PERSON>, bu ba<PERSON><PERSON><PERSON> zaten kullanılmış", "Public link copied to clipboard": "Public link copied to clipboard", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared", "Allow anyone with the link to access": "Bağlantıya sahip olan herkesin erişmesine izin ver", "Publish to internet": "<PERSON>nternett<PERSON>", "Search engine indexing": "Search engine indexing", "Disable this setting to discourage search engines from indexing the page": "Disable this setting to discourage search engines from indexing the page", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future", "{{ userName }} was added to the document": "{{ userName }} was added to the document", "{{ count }} people added to the document": "{{ count }} people added to the document", "{{ count }} people added to the document_plural": "{{ count }} people added to the document", "{{ count }} groups added to the document": "{{ count }} groups added to the document", "{{ count }} groups added to the document_plural": "{{ count }} groups added to the document", "Logo": "Logo", "Archived collections": "Archived collections", "New doc": "<PERSON><PERSON> be<PERSON>", "Empty": "Boş", "Collapse": "Dar<PERSON><PERSON>", "Expand": "Genişlet", "Document not supported – try Markdown, Plain text, HTML, or Word": "<PERSON><PERSON> destekle<PERSON> - <PERSON><PERSON>, <PERSON><PERSON><PERSON>, HTML veya Word'ü deneyin", "Go back": "<PERSON><PERSON>", "Go forward": "İleri git", "Could not load shared documents": "Could not load shared documents", "Shared with me": "Shared with me", "Show more": "<PERSON><PERSON> faz<PERSON>", "Could not load starred documents": "Could not load starred documents", "Starred": "Yıldızlı", "Up to date": "<PERSON><PERSON><PERSON><PERSON>", "{{ releasesBehind }} versions behind": "{{ <PERSON><PERSON><PERSON><PERSON> }} s<PERSON><PERSON><PERSON><PERSON><PERSON> geride", "{{ releasesBehind }} versions behind_plural": "{{ <PERSON><PERSON><PERSON><PERSON> }} s<PERSON><PERSON><PERSON><PERSON> geride", "Change permissions?": "Change permissions?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "Alfabetik olarak sıralanmış bir koleksiyondaki belgeleri yeniden sıralayamazsınız", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "Uygulamaya geri dön", "Installation": "<PERSON><PERSON><PERSON>", "Unstar document": "Unstar document", "Star document": "Star document", "Template created, go ahead and customize it": "<PERSON><PERSON><PERSON>, devam edin ve özelleştirin", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "<em>{{titleWithDefault}}</em> belgesinden şablon oluşturmak bozucu olmayan bir işlemdir – belgenin bir kopyasını oluşturacağız ve onu yeni belgeler için başlangıç noktası olarak kullanılabilecek bir şablona dönüştüreceğiz.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "Location", "Admins can manage the workspace and access billing.": "Admins can manage the workspace and access billing.", "Editors can create, edit, and comment on documents.": "Editors can create, edit, and comment on documents.", "Viewers can only view and comment on documents.": "Viewers can only view and comment on documents.", "Are you sure you want to make {{ userName }} a {{ role }}?": "Are you sure you want to make {{ userName }} a {{ role }}?", "I understand, delete": "I understand, delete", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "{{ userName }} askıya almak istediğinizden emin misiniz? Askıya alınan kullanıcıların giriş yapması engellenecektir.", "New name": "New name", "Name can't be empty": "Name can't be empty", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "New email", "Email can't be empty": "Email can't be empty", "Your import completed": "Your import completed", "Previous match": "Previous match", "Next match": "Next match", "Find and replace": "Find and replace", "Find": "Find", "Match case": "Match case", "Enable regex": "Enable regex", "Replace options": "Replace options", "Replacement": "Replacement", "Replace": "Replace", "Replace all": "Replace all", "Profile picture": "Profil fotoğrafı", "Create a new doc": "<PERSON>ni bir belge o<PERSON>", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Keep as link", "Mention": "Mention", "Embed": "Embed", "Add column after": "Add column after", "Add column before": "Add column before", "Add row after": "Add row after", "Add row before": "Add row before", "Align center": "<PERSON><PERSON> hi<PERSON>a", "Align left": "<PERSON>a hizala", "Align right": "<PERSON><PERSON><PERSON>", "Default width": "Default width", "Full width": "<PERSON> g<PERSON>", "Bulleted list": "Madde işaretli liste", "Todo list": "<PERSON><PERSON><PERSON><PERSON>", "Code block": "<PERSON><PERSON>", "Copied to clipboard": "Panoya kopyalandı", "Code": "Kod", "Comment": "<PERSON><PERSON>", "Create link": "Bağlantı oluştur", "Sorry, an error occurred creating the link": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bağlantı oluşturulurken bir hata meydana geldi", "Create a new child doc": "Create a new child doc", "Delete table": "<PERSON><PERSON><PERSON><PERSON> sil", "Delete file": "Delete file", "Width x Height": "Width x Height", "Download file": "Download file", "Replace file": "Replace file", "Delete image": "<PERSON><PERSON><PERSON> sil", "Download image": "<PERSON><PERSON><PERSON>", "Replace image": "<PERSON><PERSON><PERSON><PERSON> de<PERSON>", "Italic": "İtalik", "Sorry, that link won’t work for this embed type": "Maalesef bu ba<PERSON>lantı bu yerleştirme türü için çalışmayacak", "File attachment": "<PERSON><PERSON><PERSON>", "Enter a link": "Enter a link", "Big heading": "Büyük başlık", "Medium heading": "<PERSON><PERSON> b<PERSON>şl<PERSON>", "Small heading": "Küçük başlık", "Extra small heading": "Extra small heading", "Heading": "Başlık", "Divider": "Ayırıcı", "Image": "Resim", "Sorry, an error occurred uploading the file": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, dosya yüklenirken bir hata oluş<PERSON>", "Write a caption": "Başlık yaz", "Info": "<PERSON><PERSON><PERSON>", "Info notice": "<PERSON><PERSON><PERSON> notu", "Link": "Bağlantı", "Highlight": "<PERSON><PERSON><PERSON>", "Type '/' to insert": "Eklemek için '/' yazın", "Keep typing to filter": "Filtrelemek için yazmaya devam edin", "Open link": "Bağlantıyı aç", "Go to link": "Bağlantıya git", "Sorry, that type of link is not supported": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bu ba<PERSON><PERSON><PERSON> türü desteklenmiyor", "Ordered list": "Sıralı Liste", "Page break": "<PERSON><PERSON> sonu", "Paste a link": "Bağlantı yapıştır", "Paste a {{service}} link…": "{{service}} bağlantısını yapıştırın…", "Placeholder": "<PERSON>r tutucu", "Quote": "Alıntı", "Remove link": "Bağlantıyı kaldır", "Search or paste a link": "Bağlantı arayın veya yapıştırın", "Strikethrough": "Üstü çizili", "Bold": "Kalı<PERSON>", "Subheading": "Alt başlık", "Sort ascending": "Sort ascending", "Sort descending": "Sort descending", "Table": "<PERSON><PERSON><PERSON>", "Export as CSV": "Export as CSV", "Toggle header": "Toggle header", "Math inline (LaTeX)": "Satır içi matematik (LaTeX)", "Math block (LaTeX)": "Matematik bloğu (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "<PERSON><PERSON><PERSON>", "Tip notice": "<PERSON><PERSON><PERSON>", "Warning": "Uyarı", "Warning notice": "Uyarı notu", "Success": "Success", "Success notice": "Success notice", "Current date": "Şimdiki tarih", "Current time": "Şimdiki saat", "Current date and time": "Şimdiki tarih ve saat", "Indent": "Indent", "Outdent": "Outdent", "Video": "Video", "None": "None", "Could not import file": "Dosya içe aktarılamadı", "Unsubscribed from document": "Unsubscribed from document", "Unsubscribed from collection": "Unsubscribed from collection", "Account": "<PERSON><PERSON><PERSON>", "API & Apps": "API & Apps", "Details": "Ayrıntılar", "Security": "Güvenlik", "Features": "<PERSON><PERSON><PERSON><PERSON>", "Members": "<PERSON><PERSON><PERSON>", "Groups": "Gruplar", "API Keys": "API Keys", "Applications": "Applications", "Shared Links": "Paylaşılan Bağlantılar", "Import": "İçe aktar", "Install": "Install", "Integrations": "Entegrasyonlar", "Revoke token": "Tokenleri iptal et", "Revoke": "<PERSON><PERSON> al", "Show path to document": "Belgeye giden yolu göster", "Path to document": "Belgeye giden yol", "Group member options": "Grup üyesi seçenekleri", "Export collection": "Koleksiyonu dışarı aktar", "Rename": "<PERSON><PERSON>", "Sort in sidebar": "<PERSON>ar <PERSON> sırala", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "<PERSON>", "Comment options": "<PERSON><PERSON>", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName }} restored", "Document options": "Belge ayarları", "Choose a collection": "Bir koleksiyon seç", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Export options": "Dışa aktarma seçenekleri", "Group members": "Grup üyeleri", "Edit group": "<PERSON><PERSON><PERSON>", "Delete group": "Grubu sil", "Group options": "Grup seçenekleri", "Cancel": "İptal Et", "Import menu options": "Import menu options", "Member options": "Üye seçenekleri", "New document in <em>{{ collectionName }}</em>": "<em>{{ collectionName }}</em>'de yeni belge", "New child document": "<PERSON>ni alt belge", "Save in workspace": "Save in workspace", "Notification settings": "Notification settings", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "Revoking", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "Revizyon seçenekleri", "Share link revoked": "Paylaşım bağlantısı iptal edildi", "Share link copied": "Paylaşım bağlantısı kopyalandı", "Share options": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Go to document": "Belgeye geri dön", "Revoke link": "Bağlantıyı kaldır", "Contents": "İçindekiler", "Headings you add to the document will appear here": "Belgeye eklediğiniz başlıklar burada görünecek", "Table of contents": "İçindekiler", "Change name": "Change name", "Change email": "Change email", "Suspend user": "Suspend user", "An error occurred while sending the invite": "<PERSON><PERSON> g<PERSON>en bir hata meydana geldi", "User options": "Kullanıcı seçenekleri", "Change role": "Change role", "Resend invite": "<PERSON><PERSON>", "Revoke invite": "<PERSON><PERSON> i<PERSON> et", "Activate user": "Activate user", "template": "template", "document": "document", "published": "published", "edited": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "created the collection": "created the collection", "mentioned you in": "mentioned you in", "left a comment on": "left a comment on", "resolved a comment on": "resolved a comment on", "shared": "shared", "invited you to": "invited you to", "Choose a date": "Choose a date", "API key created. Please copy the value now as it will not be shown again.": "API key created. Please copy the value now as it will not be shown again.", "Scopes": "<PERSON><PERSON><PERSON>", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Expiration", "Never expires": "Never expires", "7 days": "7 days", "30 days": "30 days", "60 days": "60 days", "90 days": "90 days", "Custom": "Custom", "No expiration": "No expiration", "The document archive is empty at the moment.": "Belge arşivi şu anda bo<PERSON>.", "Collection menu": "Koleksiyon <PERSON>ü<PERSON>ü", "Drop documents to import": "İçe aktarılacak belgeleri buraya bırakın", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> hen<PERSON>z belge içermiyor.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ usersCount }} kullanıcı ve {{ groupsCount }} grup", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ usersCount }} kullanıcı ve {{ groupsCount }} grup", "{{ usersCount }} users and a group have access": "{{ usersCount }} kullanıcı ve bir grup eriş<PERSON> sahip", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} kullanıcı ve bir grup eriş<PERSON> sahip", "{{ usersCount }} users with access": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ usersCount }} kullanıcı", "{{ usersCount }} users with access_plural": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ usersCount }} kullanıcı", "{{ groupsCount }} groups with access": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ groupsCount }} grup", "{{ groupsCount }} groups with access_plural": "<PERSON><PERSON><PERSON><PERSON><PERSON> {{ groupsCount }} grup", "Archived by {{userName}}": "{{userName}} tarafından arşivlendi", "Sorry, an error occurred saving the collection": "Üzg<PERSON><PERSON><PERSON><PERSON>, koleksiyon kaydedilirken bir hata oluştu", "Add a description": "Açıklama ekle", "Share": "Paylaş", "Overview": "Overview", "Recently updated": "Yak<PERSON><PERSON> g<PERSON>", "Recently published": "Yakında yayınlandı", "Least recently updated": "En son g<PERSON><PERSON><PERSON>en", "A–Z": "A-Z", "Signing in": "<PERSON><PERSON><PERSON>ılıyor", "You can safely close this window once the Outline desktop app has opened": "Outline masaüstü uygulaması açıldıktan sonra bu pencereyi güvenle kapatabilirsiniz", "Error creating comment": "<PERSON><PERSON> oluşturulurken hata oluştu", "Add a comment": "<PERSON><PERSON> e<PERSON>", "Add a reply": "<PERSON><PERSON><PERSON> e<PERSON>", "Reply": "Yanıtla", "Post": "Post", "Upload image": "Upload image", "No resolved comments": "No resolved comments", "No comments yet": "Henüz yorum yapılmamış", "New comments": "New comments", "Most recent": "Most recent", "Order in doc": "Order in doc", "Resolved": "Resolved", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "<PERSON><PERSON> gü<PERSON>llenirken hata o<PERSON>", "Document is too large": "Belge çok büyük", "This document has reached the maximum size and can no longer be edited": "Bu belge maksimum boyuta ulaşmış olup artık dü<PERSON>z", "Authentication failed": "Kimlik doğrulama başarısız", "Please try logging out and back in again": "Lütfen çıkış yapmayı ve tekrar giriş yapmayı deneyin", "Authorization failed": "Yetkilendirme başarısız", "You may have lost access to this document, try reloading": "Bu belgeye erişiminizi kaybetmiş olabilirsiniz, lütfen sayfayı yenilemeyi deneyin", "Too many users connected to document": "Belgeye çok fazla kullanıcı bağlandı", "Your edits will sync once other users leave the document": "<PERSON><PERSON><PERSON> kullanıcılar belgeyi terk ettiğinde, ya<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> değişiklikler senkronize olacaktır", "Server connection lost": "<PERSON><PERSON><PERSON> bağlantısı kesildi", "Edits you make will sync once you’re online": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, çevrimiçi olduğunuzda senkronize edilir", "Document restored": "<PERSON>ge geri <PERSON>", "Images are still uploading.\nAre you sure you want to discard them?": "Resimler yüklenmeye devam ediyor.\nBunları atmak istediğinizden emin misiniz?", "{{ count }} comment": "{{ count }} yorum", "{{ count }} comment_plural": "{{ count }} yorum", "Viewed by": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "only you": "sad<PERSON>e siz", "person": "ki<PERSON>i", "people": "k<PERSON><PERSON><PERSON>", "Last updated": "<PERSON> g<PERSON><PERSON><PERSON><PERSON>", "Type '/' to insert, or start writing…": "Eklemek için '/' yazın veya yazmaya başlayın…", "Hide contents": "İçeriği gizle", "Show contents": "İçeriği göster", "available when headings are added": "available when headings are added", "Edit {{noun}}": "{{noun}}'<PERSON> düzenle", "Switch to dark": "Koyu temaya geç", "Switch to light": "Açık temaya geç", "Archived": "Arşivlendi", "Save draft": "Save draft", "Done editing": "Done editing", "Restore version": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> geri <PERSON>", "No history yet": "Tarihçe yok", "Source": "Source", "Imported from {{ source }}": "Imported from {{ source }}", "Stats": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "{{ count }} minute read": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON> okuma", "{{ count }} minute read_plural": "{{ count }} <PERSON><PERSON><PERSON><PERSON><PERSON> okuma", "{{ count }} words": "{{ count }} kelime", "{{ count }} words_plural": "{{ count }} kelime", "{{ count }} characters": "{{ count }} karakter", "{{ count }} characters_plural": "{{ count }} karakter", "{{ number }} emoji": "{{ number }} emoji", "No text selected": "<PERSON><PERSON>", "{{ count }} words selected": "{{ count }} k<PERSON><PERSON>", "{{ count }} words selected_plural": "{{ count }} k<PERSON><PERSON>", "{{ count }} characters selected": "{{ count }} ka<PERSON><PERSON>", "{{ count }} characters selected_plural": "{{ count }} ka<PERSON><PERSON>", "Contributors": "Katkıda Bulunanlar", "Created": "<PERSON><PERSON>ş<PERSON><PERSON><PERSON>", "Creator": "Oluşturan", "Last edited": "Son dü<PERSON><PERSON>me", "Previously edited": "Önceden düzenlenmiş", "No one else has viewed yet": "Hen<PERSON>z başka kimse görüntülemedi", "Viewed {{ count }} times by {{ teamMembers }} people": "{{ teamMembers }} ki<PERSON>i ta<PERSON> {{ count }} kez <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "{{ teamMembers }} ki<PERSON>i ta<PERSON> {{ count }} kez <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "Viewer insights are disabled.": "Viewer insights are disabled.", "Sorry, the last change could not be persisted – please reload the page": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, son <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> devam ettirilemedi - lüt<PERSON> sayfayı yeniden yükleyin", "{{ count }} days": "{{ count }} day", "{{ count }} days_plural": "{{ count }} days", "This template will be permanently deleted in <2></2> unless restored.": "<PERSON><PERSON> <PERSON><PERSON><PERSON>, restore edil<PERSON><PERSON><PERSON><PERSON> <2></2> içinde kalıcı olarak silinecek.", "This document will be permanently deleted in <2></2> unless restored.": "<PERSON><PERSON> belge, restore edil<PERSON><PERSON><PERSON><PERSON> <2></2> içinde kalıcı olarak silinecek.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "Bir metni seçin ve <1></1> kontolünü kullanarak yeni belgeler oluşturulurken kullanılabilecek yer tutucu ekleyin", "You’re editing a template": "Bir şablonu düzenliyorsunuz", "Deleted by {{userName}}": "{{userName}} tara<PERSON><PERSON><PERSON><PERSON> silindi", "Observing {{ userName }}": "{{ userName }} gözlemliyor", "Backlinks": "<PERSON><PERSON>", "Close": "Close", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} is using {{ appName }} to share documents, please login to continue.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "<em>{{ documentTitle }}</em> şablonunu silmek istediğinizden emin misiniz?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested documents</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "Gelecekte {{noun}}'a referans gösterme veya geri yükleme se<PERSON> is<PERSON>yo<PERSON>nız, bunun yerine onu arşivlemeyi dü<PERSON>ü<PERSON>ün.", "Select a location to move": "Select a location to move", "Document moved": "Belge taşındı", "Couldn’t move the document, try again?": "Couldn’t move the document, try again?", "Move to <em>{{ location }}</em>": "Move to <em>{{ location }}</em>", "Couldn’t create the document, try again?": "Belge oluşturulamadı, tekrar dene?", "Document permanently deleted": "Belge kalıcı olarak silindi", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "<em>{{ documentTitle }}</em> be<PERSON><PERSON><PERSON> kalıcı olarak silmek istediğinizden emin misiniz? Bu işlem hemen yapılır ve geri alınamaz.", "Select a location to publish": "Select a location to publish", "Document published": "Belge yayınlandı", "Couldn’t publish the document, try again?": "Couldn’t publish the document, try again?", "Publish in <em>{{ location }}</em>": "Publish in <em>{{ location }}</em>", "Search documents": "Belgeleri ara", "No documents found for your filters.": "Filtreleriniz için belge bulunamadı.", "You’ve not got any drafts at the moment.": "<PERSON><PERSON> anda hiç taslağınız yok.", "Payment Required": "Payment Required", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "Not found", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "Çevrimdışı", "We were unable to load the document while offline.": "Çevrimdışıyken belgeyi yükleyemedik.", "Your account has been suspended": "Hesabın<PERSON>z askıya alındı", "Warning Sign": "Uyarı işareti", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.", "Created by me": "<PERSON><PERSON>", "Weird, this shouldn’t ever be empty": "<PERSON><PERSON><PERSON>, bu <PERSON>la bo<PERSON> o<PERSON>", "You haven’t created any documents yet": "<PERSON><PERSON><PERSON><PERSON> bir belge oluşturmadınız", "Documents you’ve recently viewed will be here for easy access": "Yakın zamanda gör<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, kola<PERSON> er<PERSON><PERSON><PERSON> i<PERSON><PERSON> burada olacak", "We sent out your invites!": "Davetlerinizi gönderdik!", "Those email addresses are already invited": "Bu e-posta adresleri zaten davet edildi", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, bir seferde yalnızca {{MAX_INVITES}} davet gönderebilirsiniz", "Invited {{roleName}} will receive access to": "Invited {{roleName}} will receive access to", "{{collectionCount}} collections": "{{collectionCount}} collections", "Admin": "Yönetici", "Can manage all workspace settings": "Can manage all workspace settings", "Can create, edit, and delete documents": "Can create, edit, and delete documents", "Can view and comment": "Can view and comment", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "Yönetici olarak, <2>e-posta ile oturum açmayı da etkinleştirebilirsiniz.</2>.", "Invite as": "Invite as", "Role": "Rol", "Email": "E-posta", "Add another": "Başka birtane ekle", "Inviting": "<PERSON><PERSON> ediliyor", "Send Invites": "<PERSON><PERSON><PERSON>", "Open command menu": "Open command menu", "Forward": "Forward", "Edit current document": "Geçerli belgeyi düzenle", "Move current document": "Geçerli belgeyi taşı", "Open document history": "Belge geçmişini aç", "Jump to search": "<PERSON><PERSON><PERSON> atla", "Jump to home": "<PERSON><PERSON><PERSON>ya git", "Focus search input": "<PERSON><PERSON>", "Open this guide": "Bu kılavuzu aç", "Enter": "Enter", "Publish document and exit": "Belgeyi yayınla ve <PERSON>ık", "Save document": "Belgeyi kaydet", "Cancel editing": "Düzenlemeyi iptal et", "Collaboration": "Collaboration", "Formatting": "Biçimlendirme", "Paragraph": "Paragra<PERSON>", "Large header": "Büyük başlık", "Medium header": "<PERSON><PERSON> b<PERSON>şl<PERSON>", "Small header": "Küçük başlık", "Underline": "Altı çizili", "Undo": "<PERSON><PERSON> al", "Redo": "<PERSON><PERSON>", "Lists": "<PERSON><PERSON><PERSON>", "Toggle task list item": "Toggle task list item", "Tab": "Tab", "Indent list item": "Liste öğesinin girintisini arttır", "Outdent list item": "Liste öğesinin girintisini azalt", "Move list item up": "Liste öğesini yukarı taşı", "Move list item down": "Liste öğesini aşağı taşı", "Tables": "Tables", "Insert row": "Insert row", "Next cell": "Next cell", "Previous cell": "Previous cell", "Space": "Space", "Numbered list": "Numaralandırılmış liste", "Blockquote": "Blok alıntı", "Horizontal divider": "<PERSON><PERSON><PERSON>", "LaTeX block": "LaTeX block", "Inline code": "Satır içi kod", "Inline LaTeX": "Inline LaTeX", "Triggers": "Triggers", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Insert block", "Sign In": "<PERSON><PERSON><PERSON> ol", "Continue with Email": "E-posta ile devam et", "Continue with {{ authProviderName }}": "{{ authProviderName }} ile devam et", "Back to home": "<PERSON><PERSON><PERSON><PERSON> g<PERSON>n", "The workspace could not be found": "The workspace could not be found", "To continue, enter your workspace’s subdomain.": "To continue, enter your workspace’s subdomain.", "subdomain": "subdomain", "Continue": "<PERSON><PERSON> et", "The domain associated with your email address has not been allowed for this workspace.": "The domain associated with your email address has not been allowed for this workspace.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.", "The workspace you authenticated with is not authorized on this installation. Try another?": "The workspace you authenticated with is not authorized on this installation. Try another?", "We could not read the user info supplied by your identity provider.": "We could not read the user info supplied by your identity provider.", "Your account uses email sign-in, please sign-in with email to continue.": "Your account uses email sign-in, please sign-in with email to continue.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Authentication failed – we were unable to sign you in at this time. Please try again.", "Authentication failed – you do not have permission to access this workspace.": "Authentication failed – you do not have permission to access this workspace.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "Your account has been suspended. To re-activate your account, please contact a workspace admin.", "This workspace has been suspended. Please contact support to restore access.": "This workspace has been suspended. Please contact support to restore access.", "Authentication failed – this login method was disabled by a workspace admin.": "Authentication failed – this login method was disabled by a workspace admin.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "Workspace name", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "Oturum aç", "Error": "Error", "Failed to load configuration.": "Konfigürasyon <PERSON>.", "Check the network requests and server logs for full details of the error.": "Check the network requests and server logs for full details of the error.", "Custom domain setup": "Custom domain setup", "Almost there": "Almost there", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.", "Choose workspace": "Choose workspace", "This login method requires choosing your workspace to continue": "This login method requires choosing your workspace to continue", "Check your email": "E-postanı kontrol et", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "<PERSON><PERSON> hesap varsa, <em>{{ emailLinkSentTo }}</em> e-postasına sihirli bir oturum açma bağlantısı gönderildi.", "Back to login": "Oturum açma ekranına geri dön", "Get started by choosing a sign-in method for your new workspace below…": "Get started by choosing a sign-in method for your new workspace below…", "Login to {{ authProviderName }}": "{{ authProviderName }} ile giri<PERSON> yap", "You signed in with {{ authProviderName }} last time.": "En son {{ authProviderName }} ile giriş yaptı<PERSON>z.", "Or": "Or", "Already have an account? Go to <1>login</1>.": "Zaten hesabınız var mı? <1>Oturum açın</1>.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "Her<PERSON><PERSON> bir koleksiyon", "All time": "All time", "Past day": "<PERSON><PERSON><PERSON><PERSON> gün", "Past week": "Geçen hafta", "Past month": "Geçen ay", "Past year": "Geçen yıl", "Any time": "<PERSON><PERSON><PERSON> bir zaman", "Remove document filter": "Remove document filter", "Any status": "Any status", "Remove search": "Aramayı kaldır", "Any author": "<PERSON><PERSON><PERSON> bir yazar", "Search titles only": "Search titles only", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "Arama filtreleriniz için belge bulunamadı.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "Last used", "No expiry": "No expiry", "Restricted scope": "Restricted scope", "API key copied to clipboard": "API key copied to clipboard", "Copied": "<PERSON>pied", "Are you sure you want to revoke the {{ tokenName }} token?": "Are you sure you want to revoke the {{ tokenName }} token?", "Disconnect integration": "Disconnect integration", "Connected": "Connected", "Disconnect": "Bağlantıyı kes", "Disconnecting": "Disconnecting", "Allowed domains": "Allowed domains", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.", "Remove domain": "Remove domain", "Add a domain": "Add a domain", "Save changes": "Save changes", "Please choose a single file to import": "Lütfen içe aktarmak için tek bir dosya seçin", "Your import is being processed, you can safely leave this page": "İçe aktarımınız işleniyor, bu <PERSON><PERSON><PERSON> güvenle ayrılabilirsiniz", "File not supported – please upload a valid ZIP file": "Dosya desteklenmiyor - lütfen geçerli bir ZIP <PERSON> yü<PERSON>in", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "Yükleniyor", "Start import": "Start import", "Processing": "İşleniyor", "Expired": "S<PERSON><PERSON>i doldu", "Completed": "Completed", "Failed": "Başarısız oldu", "All collections": "<PERSON><PERSON><PERSON>", "Import deleted": "Import deleted", "Export deleted": "<PERSON><PERSON><PERSON><PERSON> aktarma silindi", "Are you sure you want to delete this import?": "Are you sure you want to delete this import?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.", "Check server logs for more details.": "Check server logs for more details.", "{{userName}} requested": "{{userName}} istedi", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "Gruplar takımınızı organize etmek içindir. Bir işlev veya sorumluluk etrafında toplandığında en iyi şekilde çalışırlar - örneğin Destek veya Mühendislik.", "You’ll be able to add people to the group next.": "Daha sonra gruba kişi ekleyebileceksiniz.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "Bu grubun adını istediğiniz zaman düzenleyebilirsiniz, ancak bunu çok sık yapmak takım arkadaşlarınızın kafasını karıştırabilir.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "Bundan emin misiniz? <em>{{groupName}}</em> g<PERSON><PERSON><PERSON>, üyelerinin ilişki<PERSON> olduğu koleksiyonlara ve belgelere erişimini kaybetmesine neden olur.", "Add people to {{groupName}}": "{{groupName}} g<PERSON><PERSON><PERSON> ki<PERSON>i e<PERSON>", "{{userName}} was removed from the group": "{{userName}} g<PERSON><PERSON>", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.", "Add people": "<PERSON><PERSON><PERSON>", "Listing members of the <em>{{groupName}}</em> group.": "Listing members of the <em>{{groupName}}</em> group.", "This group has no members.": "Bu grubun hiç <PERSON> yok.", "{{userName}} was added to the group": "{{userName}} gruba eklendi", "Could not add user": "Kullanıcı eklenemedi", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Add members below to give them access to the group. Need to add someone who’s not yet a member?", "Invite them to {{teamName}}": "{{teamName}} ta<PERSON><PERSON><PERSON><PERSON><PERSON> davet et", "Ask an admin to invite them first": "Ask an admin to invite them first", "Search by name": "<PERSON> göre ara", "Search people": "<PERSON><PERSON><PERSON> ara", "No people matching your search": "Aramanızla eşleşen kişi yok", "No people left to add": "Eklenecek kişi kalmadı", "Date created": "Date created", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "Bu nasıl çalışıyor?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload", "Canceled": "Canceled", "Import canceled": "Import canceled", "Are you sure you want to cancel this import?": "Are you sure you want to cancel this import?", "Canceling": "Canceling", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "Daha önce Outline kurulumundan dışa aktarılan bir zip dosyasını içe aktarabilirsiniz; koleksiyonlar, belgeler ve görüntüler içe aktarılacaktır. Outline'da, Ayarlar kenar çubuğunda <em>Dışa Aktar</em>'ı açın ve <em>Verileri Dışa Aktar</em>'a tıklayın.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload", "Configure": "Configure", "Connect": "Bağla", "Last active": "Son aktiflik", "Guest": "Guest", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "Shared by", "Date shared": "Paylaşılan tarih", "Last accessed": "<PERSON> <PERSON><PERSON><PERSON><PERSON>", "Domain": "Domain", "Views": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "All roles": "All roles", "Admins": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Editors": "Editors", "All status": "All status", "Active": "Aktif", "Left": "Left", "Right": "Right", "Settings saved": "<PERSON><PERSON><PERSON>", "Logo updated": "<PERSON><PERSON>", "Unable to upload new logo": "Yeni logo yüklenemiyor", "Delete workspace": "Delete workspace", "These settings affect the way that your workspace appears to everyone on the team.": "These settings affect the way that your workspace appears to everyone on the team.", "Display": "Display", "The logo is displayed at the top left of the application.": "Logo, uygulamanın sol üst kısmında görüntülenir.", "The workspace name, usually the same as your company name.": "The workspace name, usually the same as your company name.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "Theme", "Customize the interface look and feel.": "Customize the interface look and feel.", "Reset theme": "Reset theme", "Accent color": "Accent color", "Accent text color": "Accent text color", "Public branding": "Public branding", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Table of contents position", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "Behavior", "Subdomain": "Alt alan adı", "Your workspace will be accessible at": "Your workspace will be accessible at", "Choose a subdomain to enable a login page just for your team.": "Yalnızca ekibiniz için bir giriş sayfasını etkinleştirmek için bir alt alan adı seçin.", "This is the screen that workspace members will first see when they sign in.": "This is the screen that workspace members will first see when they sign in.", "Danger": "Danger", "You can delete this entire workspace including collections, documents, and users.": "You can delete this entire workspace including collections, documents, and users.", "Export data": "Export data", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "<PERSON> dı<PERSON><PERSON> a<PERSON>", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.", "Separate editing": "Separate editing", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.", "When enabled team members can add comments to documents.": "When enabled team members can add comments to documents.", "Create a group": "Grup oluştur", "Could not load groups": "Could not load groups", "New group": "<PERSON><PERSON> grup", "Groups can be used to organize and manage the people on your team.": "<PERSON><PERSON><PERSON>, ekibinizdeki kişileri organize etmek ve yönetmek için kullanılabilir.", "No groups have been created yet": "Henüz grup oluşturulmadı", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)", "Import data": "Verileri içe aktar", "Import a JSON data file exported from another {{ appName }} instance": "Import a JSON data file exported from another {{ appName }} instance", "Import pages from a Confluence instance": "Confluence'da<PERSON> say<PERSON>ı içe aktarma", "Enterprise": "Enterprise", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.", "Recent imports": "Son i<PERSON>e a<PERSON>", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.", "Receive a notification whenever a new document is published": "Yeni bir belge yayınlandığında bildirim alın", "Document updated": "<PERSON><PERSON>", "Receive a notification when a document you are subscribed to is edited": "Receive a notification when a document you are subscribed to is edited", "Comment posted": "Comment posted", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment", "Mentioned": "Mentioned", "Receive a notification when someone mentions you in a document or comment": "Receive a notification when someone mentions you in a document or comment", "Receive a notification when a comment thread you were involved in is resolved": "Receive a notification when a comment thread you were involved in is resolved", "Collection created": "Koleksiyon oluşturuldu", "Receive a notification whenever a new collection is created": "Yeni bir koleksiyon oluşturulduğunda bildirim alın", "Invite accepted": "<PERSON><PERSON><PERSON> accepted", "Receive a notification when someone you invited creates an account": "Receive a notification when someone you invited creates an account", "Invited to document": "Invited to document", "Receive a notification when a document is shared with you": "Receive a notification when a document is shared with you", "Invited to collection": "Invited to collection", "Receive a notification when you are given access to a collection": "Receive a notification when you are given access to a collection", "Export completed": "Export completed", "Receive a notification when an export you requested has been completed": "Receive a notification when an export you requested has been completed", "Getting started": "Başlarken", "Tips on getting started with features and functionality": "Tips on getting started with features and functionality", "New features": "<PERSON><PERSON>", "Receive an email when new features of note are added": "Outline'ın yeni özellikleri eklendiğinde bir e-posta alın", "Notifications saved": "<PERSON><PERSON><PERSON><PERSON><PERSON> ka<PERSON>", "Unsubscription successful. Your notification settings were updated": "Abonelikten çıkma başarılı. <PERSON><PERSON><PERSON><PERSON>ı<PERSON>ü<PERSON>", "Manage when and where you receive email notifications.": "E-posta bildirimlerini ne zaman ve nerede alacağınızı yönetin.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "E-posta entegrasyonu şu anda devre dışı. Lütfen ilişkili ortam değişkenlerini ayarlayın ve bildirimleri etkinleştirmek için sunucuyu yeniden başlatın.", "Preferences saved": "Preferences saved", "Delete account": "Hesabı sil", "Manage settings that affect your personal experience.": "Manage settings that affect your personal experience.", "Language": "Dil", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.", "Choose your preferred interface color scheme.": "Choose your preferred interface color scheme.", "Use pointer cursor": "Use pointer cursor", "Show a hand cursor when hovering over interactive elements.": "Show a hand cursor when hovering over interactive elements.", "Show line numbers": "Show line numbers", "Show line numbers on code blocks in documents.": "Show line numbers on code blocks in documents.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.", "Remember previous location": "Remember previous location", "Automatically return to the document you were last viewing when the app is re-opened.": "Automatically return to the document you were last viewing when the app is re-opened.", "Smart text replacements": "Smart text replacements", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.", "You may delete your account at any time, note that this is unrecoverable": "Hesabınızı istediğiniz zaman silebilirsiniz, bunun kurtarılamaz olduğunu unut<PERSON>yın", "Profile saved": "<PERSON><PERSON>", "Profile picture updated": "Profil fotoğrafı güncellendi", "Unable to upload new profile picture": "Yeni profil fotoğrafı yüklenemiyor", "Manage how you appear to other members of the workspace.": "Manage how you appear to other members of the workspace.", "Photo": "Fotoğraf", "Choose a photo or image to represent yourself.": "Kendinizi temsil edecek bir fotoğraf veya resim seçin.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "<PERSON><PERSON>, gerçek adınız veya takma adınız olabilir - ancak insanların size atıfta bulunmasını istersiniz.", "Email address": "E-posta adresi", "Are you sure you want to require invites?": "Are you sure you want to require invites?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.", "Settings that impact the access, security, and content of your workspace.": "Settings that impact the access, security, and content of your workspace.", "Allow members to sign-in with {{ authProvider }}": "Allow members to sign-in with {{ authProvider }}", "Disabled": "Disabled", "Allow members to sign-in using their email address": "Allow members to sign-in using their email address", "The server must have SMTP configured to enable this setting": "Bu ayarı etkinleştirmek için sunucuda SMTP yapılandırılmış olmalıdır", "Access": "Access", "Allow users to send invites": "Allow users to send invites", "Allow editors to invite other people to the workspace": "Allow editors to invite other people to the workspace", "Require invites": "Require invites", "Require members to be invited to the workspace before they can create an account using SSO.": "Require members to be invited to the workspace before they can create an account using SSO.", "Default role": "Varsayılan rol", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "Yeni hesaplar için varsayılan kullanıcı rolü. Bu ayarın değiştirilmesi mevcut kullanıcı hesaplarını etkilemez.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "When enabled, documents can be shared publicly on the internet by any member of the workspace", "Viewer document exports": "Viewer document exports", "When enabled, viewers can see download options for documents": "When enabled, viewers can see download options for documents", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "<PERSON><PERSON> hizmet <PERSON>", "Links to supported services are shown as rich embeds within your documents": "Desteklenen hizmetlere b<PERSON><PERSON>, belgelerinizde zengin <PERSON>eler olarak gösterilir", "Collection creation": "Koleksiyon oluşturma", "Allow editors to create new collections within the workspace": "Allow editors to create new collections within the workspace", "Workspace creation": "Workspace creation", "Allow editors to create new workspaces": "Allow editors to create new workspaces", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "<PERSON><PERSON><PERSON><PERSON><PERSON> şu anda devre dışı.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "Herkese açık belge paylaşımını <em>güvenlik ayarlarında</em> global olarak etkinleştirebilir ve devre dışı bırakabilirsiniz.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "Paylaşılan belgeler aşağıda listelenmiştir. Genel bağlantıya sahip olan her<PERSON>, bağlantı iptal edilene kadar belgenin salt okunur bir sürümüne erişebilir.", "You can create templates to help your team create consistent and accurate documentation.": "Ekibinizin tutarlı ve doğru belgeler oluşturmasına yardımcı olacak şablonlar oluşturabilirsiniz.", "Alphabetical": "Alfabetik", "There are no templates just yet.": "Henüz şablon yok.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.", "Confirmation code": "Confirmation code", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "You are creating a new workspace using your current account — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "To create a workspace under another email please sign up from the homepage", "Trash emptied": "Trash emptied", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.", "Recently deleted": "Recently deleted", "Trash is empty at the moment.": "Çöp kutusu <PERSON>u anda bo<PERSON>.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.", "Delete my account": "Delete my account", "Today": "<PERSON><PERSON><PERSON><PERSON>", "Yesterday": "<PERSON><PERSON><PERSON>", "Last week": "Geçen hafta", "This month": "<PERSON>u ay", "Last month": "Geçen ay", "This year": "<PERSON><PERSON> yıl", "Expired yesterday": "Expired yesterday", "Expired {{ date }}": "Expired {{ date }}", "Expires today": "Expires today", "Expires tomorrow": "Expires tomorrow", "Expires {{ date }}": "Expires {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?", "Something went wrong while authenticating your request. Please try logging in again.": "Something went wrong while authenticating your request. Please try logging in again.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.", "Enabled by {{integrationCreatedBy}}": "Enabled by {{integrationCreated<PERSON>y}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "Instance URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "Site ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "document published": "belge yayın<PERSON>ı", "document updated": "be<PERSON>", "Posting to the <em>{{ channelName }}</em> channel on": "<em>{{ channelName }}</em> kanalına gönderiliyor", "These events should be posted to Slack": "Bu olaylar Slack'e gönderilmelidir", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?", "Personal account": "Personal account", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Disconnecting your personal account will prevent searching for documents from S<PERSON>ck. Are you sure?", "Slash command": "Slash command", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "How to use {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.", "Post to Channel": "Post to Channel", "This is what we found for \"{{ term }}\"": "This is what we found for \"{{ term }}\"", "No results for \"{{ term }}\"": "No results for \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "Link your account", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Are you sure you want to delete the {{ name }} webhook?", "Webhook updated": "Webhook updated", "Update": "Update", "Updating": "Updating", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.", "A memorable identifer": "A memorable identifer", "URL": "URL", "Signing secret": "Signing secret", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.", "All events": "All events", "All {{ groupName }} events": "All {{ groupName }} events", "Delete webhook": "Delete webhook", "Subscribed events": "Subscribed events", "Edit webhook": "Edit webhook", "Webhook created": "Webhook created", "Webhooks": "Webhooks", "New webhook": "New webhook", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.", "Inactive": "Inactive", "Create a webhook": "Create a webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.", "Never logged in": "Never logged in", "Online now": "Online now", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Viewed just now", "You updated {{ timeAgo }}": "You updated {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} updated {{ timeAgo }}", "You created {{ timeAgo }}": "You created {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} created {{ timeAgo }}", "Error loading data": "Error loading data"}