{"New API key": "새 API 키", "Open collection": "컬렉션 열기", "New collection": "새 컬렉션", "Create a collection": "컬렉션 만들기", "Edit": "편집", "Edit collection": "컬렉션 편집", "Permissions": "권한", "Collection permissions": "컬렉션 권한", "Share this collection": "컬렉션 공유하기", "Search in collection": "컬렉션 검색", "Star": "중요 문서 표시", "Unstar": "중요 문서 해제", "Subscribe": "구독", "Subscribed to document notifications": "문서에 대해 알림 구독됨", "Unsubscribe": "구독 해제", "Unsubscribed from document notifications": "문서에 대해 알림 구독 해제됨", "Archive": "보관", "Archive collection": "보관된 컬렉션", "Collection archived": "컬렉션 보관됨", "Archiving": "아카이빙", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "이 컬렉션을 보관하면 컬렉션에 포함된 문서 또한 보관됩니다. 컬렉션의 문서는 더 이상 검색 결과에 표시되지 않습니다.", "Restore": "복원하기", "Collection restored": "컬렉션 복구됨", "Delete": "삭제", "Delete collection": "컬렉션 삭제", "New template": "새 템플릿", "Delete comment": "댓글 삭제", "Mark as resolved": "해결됨으로 표시", "Thread resolved": "스레드 해결됨", "Mark as unresolved": "미해결로 표시", "View reactions": "컬렉션 보기", "Reactions": "반응", "Copy ID": "ID 복사", "Clear IndexedDB cache": "IndexedDB 캐시 삭제", "IndexedDB cache cleared": "IndexedDB 캐시 삭제됨", "Toggle debug logging": "디버그 로그 표시", "Debug logging enabled": "디버그 로깅 활성화", "Debug logging disabled": "디버그 로깅 비활성화", "Development": "개발", "Open document": "문서 열기", "New document": "새 문서", "New draft": "새 초안", "New from template": "새 템플릿 문서", "New nested document": "새 하위 문서", "Publish": "게시하기", "Published {{ documentName }}": "{{ documentName }} 이(가) 발행됨", "Publish document": "문서 게시", "Unpublish": "게시 취소", "Unpublished {{ documentName }}": "{{ documentName }} 이(가) 발행 취소됨", "Share this document": "이 문서 공유하기", "HTML": "HTML", "PDF": "PDF", "Exporting": "내보내는 중", "Markdown": "마크다운", "Download": "다운로드", "Download document": "문서 다운로드", "Copy as Markdown": "마크다운으로 복사", "Markdown copied to clipboard": "마크다운이 클립보드에 복사됨", "Copy as text": "텍스트로 복사", "Text copied to clipboard": "텍스트가 클립보드에 복사됨", "Copy public link": "공개 링크 복사", "Link copied to clipboard": "링크가 클립보드로 복사 되었습니다", "Copy link": "링크 복사", "Copy": "복사", "Duplicate": "복사하기", "Duplicate document": "문서 복제", "Copy document": "문서 복사", "collection": "컬렉션", "Pin to {{collectionName}}": "{{collectionName}}에 고정", "Pinned to collection": "컬렉션에 고정됨", "Pin to home": "홈에 고정", "Pinned to home": "홈에 고정됨", "Pin": "고정", "Search in document": "문서 검색", "Print": "인쇄", "Print document": "문서 인쇄", "Import document": "문서 가져오기", "Templatize": "템플릿화", "Create template": "템플릿 만들기", "Open random document": "무작위 문서 열기", "Search documents for \"{{searchQuery}}\"": "\"{{searchQuery}}\" 에 대한 문서 검색", "Move to workspace": "워크스페이스로 이동", "Move": "이동", "Move to collection": "컬렉션으로 이동", "Move {{ documentType }}": "{{ documentType }} 문서 이동", "Are you sure you want to archive this document?": "정말로 이 문서를 아카이브 하시겠습니까?", "Document archived": "문서가 보관되었습니다", "Archiving this document will remove it from the collection and search results.": "이 문서를 보관하면 컬렉션 및 검색 결과에서 제거됩니다.", "Delete {{ documentName }}": "{{ documentName }} 삭제", "Permanently delete": "영구 삭제", "Permanently delete {{ documentName }}": "{{ documentName }} 를 영구 삭제", "Empty trash": "휴지통 비우기", "Permanently delete documents in trash": "휴지통의 문서를 완전히 삭제합니다", "Comments": "댓글", "History": "히스토리", "Insights": "문서 정보", "Disable viewer insights": "뷰어 정보 비활성화", "Enable viewer insights": "뷰어 정보 활성화", "Leave document": "문서 나가기", "You have left the shared document": "공유 문서 나갔습니다", "Could not leave document": "문서를 나갈 수 없습니다", "Home": "홈", "Drafts": "임시 보관함", "Search": "검색", "Trash": "휴지통", "Settings": "설정", "Profile": "사용자 정보", "Templates": "템플릿", "Notifications": "알림", "Preferences": "기본 설정", "Documentation": "문서", "API documentation": "API 문서", "Toggle sidebar": "사이드바 전환", "Send us feedback": "의견 보내기", "Report a bug": "버그 신고", "Changelog": "변경 사항", "Keyboard shortcuts": "단축키", "Download {{ platform }} app": "{{ platform }} 앱 다운로드", "Log out": "로그아웃", "Mark notifications as read": "알림을 읽은 상태로 표시", "Archive all notifications": "모든 알림 보관", "New App": "새로운 앱", "New Application": "새 애플리케이션", "This version of the document was deleted": "이 문서의 버전은 삭제됨", "Link copied": "링크 복사됨", "Dark": "어둡게 보기", "Light": "밝게 보기", "System": "시스템", "Appearance": "외관", "Change theme": "테마 변경", "Change theme to": "다음으로 테마 변경", "Switch workspace": "워크스페이스 전환", "Select a workspace": "워크스페이스 선택", "New workspace": "새 워크스페이스", "Create a workspace": "워크스페이스 만들기", "Login to workspace": "워크스페이스에 로그인", "Invite people": "초대하기", "Invite to workspace": "워크스페이스에 초대", "Promote to {{ role }}": "{{ role }} (으)로 승격", "Demote to {{ role }}": "{{ role }} (으)로 강등", "Update role": "역할 업데이트", "Delete user": "사용자 삭제", "Collection": "컬렉션", "Collections": "컬렉션", "Debug": "디버그", "Document": "문서", "Documents": "문서", "Recently viewed": "최근에 조회됨", "Revision": "리비전", "Navigation": "네비게이션", "Notification": "알림", "People": "사용자", "Workspace": "워크스페이스", "Recent searches": "최근 검색", "currently editing": "현재 수정 중", "currently viewing": "현재 보는 중", "previously edited": "이전에 수정됨", "You": "본인", "Viewers": "열람자", "Collections are used to group documents and choose permissions": "컬렉션은 문서를 그룹화하고 권한을 지정하는 데 사용됩니다", "Name": "이름", "The default access for workspace members, you can share with more users or groups later.": "워크스페이스 멤버에 대한 기본 권한으로, 나중에 더 많은 사용자나 그룹과 공유할 수 있습니다.", "Public document sharing": "인터넷 공유 허용", "Allow documents within this collection to be shared publicly on the internet.": "이 컬렉션 내의 모든 문서를 인터넷에 공개적으로 공유하도록 허용합니다.", "Commenting": "댓글 달기", "Allow commenting on documents within this collection.": "이 컬렉션 내 문서에 대한 댓글을 허용합니다.", "Saving": "저장 중", "Save": "저장", "Creating": "생성 중", "Create": "생성", "Collection deleted": "컬렉션 삭제됨", "I’m sure – Delete": "이해했습니다 – 삭제", "Deleting": "삭제 중", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "정말로 <em>{{collectionName}}</em> 컬렉션을 삭제하시겠습니까? 컬렉션 삭제는 영구적이며 복원할 수 없지만 그 안에 있는 문서는 휴지통으로 이동됩니다", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "또한 <em>{{collectionName}}</em> 는(은) 초기 화면으로 사용 중입니다. 삭제하면 초기 화면이 홈 페이지로 재설정됩니다.", "Type a command or search": "명령어를 입력하거나 검색", "Choose a template": "템플릿 선택", "Are you sure you want to permanently delete this entire comment thread?": "이 쓰레드에 있는 댓글들을 영구적으로 삭제하시겠습니까?", "Are you sure you want to permanently delete this comment?": "정말로 이 댓글을 영구적으로 삭제하시겠습니까?", "Confirm": "확인", "manage access": "접근 관리", "view and edit access": "보기 및 편집 액세스", "view only access": "보기 전용 액세스", "no access": "접근 불가", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "{{ documentName }}을(를) {{ collectionName }} 컬렉션으로 이동할 수 있는 권한이 없습니다", "Move document": "문서 이동", "Moving": "이동 중", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "문서 <em>{{ title }}</em>을(를) {{ newCollectionName }} 컬렉션으로 이동하면 모든 작업 공간 멤버의 권한이 <em>{{ prevPermission }}</em>에서 <em>{{ newPermission }}</em>으로 변경됩니다.", "Submenu": "하위 메뉴", "Collections could not be loaded, please reload the app": "컬렉션을 불러올 수 없습니다. 앱을 새로고침하세요", "Default collection": "기본 컬렉션", "Start view": "시작 화면", "Install now": "지금 설치", "Deleted Collection": "삭제 된 콜렉션", "Untitled": "제목없음", "Unpin": "고정 해제", "{{ minutes }}m read": "{{ minutes }}분 전에 읽음", "Select a location to copy": "복사할 위치 선택\n", "Document copied": "문서가 복사되었습니다", "Couldn’t copy the document, try again?": "문서를 복사할 수 없습니다. 다시 시도하시겠습니까?", "Include nested documents": "중첩된 문서 포함", "Copy to <em>{{ location }}</em>": "<em>{{ location }}</em>로 복사", "Search collections & documents": "컬렉션 및 문서 검색", "No results found": "결과가 없습니다.", "New": "신규", "Only visible to you": "나에게만 보임", "Draft": "임시보관", "Template": "템플릿", "You updated": "내가 수정함:", "{{ userName }} updated": "{{ userName }} 이(가) 수정함:", "You deleted": "내가 삭제함:", "{{ userName }} deleted": "{{ userName }}이(가) 삭제함:", "You archived": "내가 보관함:", "{{ userName }} archived": "{{ userName }} 이(가) 보관함", "Imported": "가져왔습니다", "You created": "내가 생성함:", "{{ userName }} created": "{{ userName }} 이(가) 생성함:", "You published": "내가 게시함:", "{{ userName }} published": "{{ userName }} 이(가) 게시함:", "Never viewed": "미조회", "Viewed": "조회", "in": "•", "nested document": "하위 문서", "nested document_plural": "하위 문서들", "{{ total }} task": "{{ total }} 개의 일", "{{ total }} task_plural": "{{ total }} 개의 일", "{{ completed }} task done": "{{ completed }} 작업 완료", "{{ completed }} task done_plural": "{{ completed }} 작업 완료", "{{ completed }} of {{ total }} tasks": "{{ total }} 중 {{ completed }} 개의 일", "Currently editing": "현재 수정 중", "Currently viewing": "현재 보는 중", "Viewed {{ timeAgo }}": "{{ timeAgo }} 전에 봄", "Module failed to load": "모듈 불러오기 실패", "Loading Failed": "불러오기 실패", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "죄송합니다, 일부 애플리케이션을 불러올 수 없습니다. 탭을 연 이후 업데이트 되었거나 네트워크 요청이 실패했기 때문일 수 있습니다. 새로고침을 시도해보세요", "Reload": "새로고침", "Something Unexpected Happened": "예기치 않은 오류가 발생했습니다", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "죄송합니다. 복구할 수 없는 오류가 발생했습니다{{notified}} 페이지를 새로고침해 보세요. 일시적인 오류일 수 있습니다.", "our engineers have been notified": "저희 엔지니어들에게 전달되었습니다", "Show detail": "자세히 보기", "Revision deleted": "수정본 삭제됨", "Current version": "현재 버전", "{{userName}} edited": "{{userName}} 이(가) 수정함", "{{userName}} archived": "{{userName}} 이(가) 보관함", "{{userName}} restored": "{{userName}} 이(가) 복원함", "{{userName}} deleted": "{{userName}} 이(가) 삭제함", "{{userName}} added {{addedUserName}}": "{{userName}} 이(가) {{addedUserName}} 을(를) 추가함", "{{userName}} removed {{removedUserName}}": "{{userName}} 이(가) {{removedUserName}} 을(를) 제거함", "{{userName}} moved from trash": "{{userName}} 이(가) 휴지통에서 이동함", "{{userName}} published": "{{userName}} 이(가) 게시함", "{{userName}} unpublished": "{{userName}} 이(가) 게시 취소", "{{userName}} moved": "{{userName}} 이(가) 이동함", "Export started": "내보내기 시작됨", "Your file will be available in {{ location }} soon": "잠시 후 {{ location }} 에서 파일 사용 가능", "View": "보기", "A ZIP file containing the images, and documents in the Markdown format.": "마크다운 형식의 이미지 및 문서가 포함된 ZIP 파일.", "A ZIP file containing the images, and documents as HTML files.": "HTML 형식의 이미지 및 문서가 포함된 ZIP 파일.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "호환되는 다른 {{ appName }} 인스턴스로 데이터를 전송하는 데 사용할 수 있는 구조화된 데이터입니다.", "Export": "내보내기", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "<em>{{collectionName}}</em>컬렉션을 내보내기에는 시간이 걸릴 수 있습니다.", "You will receive an email when it's complete.": "완료 시점에 메일을 수신받게 됩니다.", "Include attachments": "첨부파일 포함", "Including uploaded images and files in the exported data": "내보낼 데이터에 업로드된 이미지 및 파일 포함", "{{count}} more user": "{{count}}명의 추가 사용자", "{{count}} more user_plural": "{{count}}명의 추가 사용자", "Filter": "필터", "No results": "결과 없음", "{{authorName}} created <3></3>": "{{authorName }} 이(가) <3></3> 생성", "{{authorName}} opened <3></3>": "{{authorName }} 이(가) <3></3> 열람", "Search emoji": "이모지 검색", "Search icons": "아이콘 검색", "Choose default skin tone": "기본 피부색 지정", "Show menu": "메뉴 보기", "Icon Picker": "아이콘 선택기", "Icons": "아이콘", "Emojis": "이모지", "Remove": "제거", "All": "전체", "Frequently Used": "자주 사용됨", "Search Results": "검색 결과", "Smileys & People": "스마일리 & 사람", "Animals & Nature": "동물 & 자연", "Food & Drink": "음식 & 음료", "Activity": "활동", "Travel & Places": "여행 & 장소", "Objects": "사물", "Symbols": "상징", "Flags": "깃발", "Select a color": "색상 선택", "Loading": "로딩 중", "Permission": "권한", "View only": "보기 전용", "Can edit": "편집 가능", "No access": "접근 불가", "Default access": "기본 권한", "Change Language": "언어 변경", "Dismiss": "닫기", "You’re offline.": "오프라인 상태입니다.", "Sorry, an error occurred.": "죄송합니다. 오류가 발생했습니다.", "Click to retry": "클릭하여 재시도하기", "Back": "뒤로 가기", "Unknown": "알 수 없음", "Mark all as read": "모두 읽은 상태로 표시", "You're all caught up": "모두 확인함", "Icon": "아이콘", "My App": "내 앱", "Tagline": "태그라인", "A short description": "간단한 설명", "Callback URLs": "콜백 URL", "Published": "게시됨", "Allow this app to be installed by other workspaces": "다른 작업 공간에서 이 앱을 설치하도록 허용", "{{ username }} reacted with {{ emoji }}": "{{ username }} 사용자가 {{ emoji }}로 반응함", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} 사용자와 {{ secondUsername }} 사용자가 {{ emoji }}로 반응함", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} 사용자와 {{ count }}명이 {{ emoji }}로 반응함", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} 사용자와 {{ count }}명이 {{ emoji }}로 반응함", "Add reaction": "리액션 추가", "Reaction picker": "반응 선택기", "Could not load reactions": "반응을 불러올 수 없음", "Reaction": "리액션", "Results": "결과", "No results for {{query}}": "{{query}} 에 대한 검색결과가 없습니다.", "Manage": "관리", "All members": "모든 구성원", "Everyone in the workspace": "워크스페이스의 모든 구성원", "{{ count }} member": "{{ count }}명", "{{ count }} member_plural": "{{ count }}명", "Invite": "초대", "{{ userName }} was added to the collection": "{{ userName }} 이(가) 컬렉션에 추가되었습니다", "{{ count }} people added to the collection": "사용자 {{ count }} 명이 컬렉션에 추가됨", "{{ count }} people added to the collection_plural": "사용자 {{ count }} 명이 컬렉션에 추가됨", "{{ count }} people and {{ count2 }} groups added to the collection": "사용자 {{ count }} 명과 그룹 {{ count2 }} 개가 컬렉션에 추가됨", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "사용자 {{ count }} 명과 그룹 {{ count2 }} 개가 컬렉션에 추가됨", "Add": "추가", "Add or invite": "추가 또는 초대", "Viewer": "뷰어", "Editor": "편집기", "Suggestions for invitation": "초대 제안", "No matches": "일치하는 결과 없음", "Can view": "보기 가능", "Everyone in the collection": "컬렉션의 모든 구성원", "You have full access": "전체 접근 권한 소유함", "Created the document": "문서를 만들었습니다", "Other people": "기타 인원", "Other workspace members may have access": "다른 워크스페이스 멤버들이 접근할 수 있음", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "이 문서는 접근 권한이 없는 상위 문서 또는 컬렉션을 통해 더 많은 워크스페이스 멤버들과 공유될 수 있음", "Access inherited from collection": "컬렉션으로부터 상속된 접근 권한", "{{ userName }} was removed from the document": "{{ userName }} 이(가) 문서에서 제거됨", "Could not remove user": "사용자를 제거할 수 없습니다", "Permissions for {{ userName }} updated": "{{ userName }} 에 대한 권한 업데이트됨", "Could not update user": "사용자 정보를 업데이트하지 못했습니다.", "Has access through <2>parent</2>": "<2>상위 문서</2> 권한 설정으로 인해 접근 가능", "Suspended": "사용 중지됨", "Invited": "초대됨", "Active <1></1> ago": "<1></1> 전에 활동", "Never signed in": "로그인 한 적 없음", "Leave": "나가기", "Only lowercase letters, digits and dashes allowed": "소문자, 숫자, 대시만 허용됩니다.", "Sorry, this link has already been used": "죄송하지만, 이 링크는 이미 사용중입니다.", "Public link copied to clipboard": "공개 링크를 클립보드에 복사했습니다", "Web": "웹", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "상위 문서인 <2>{{documentTitle}}</2> 이(가) 공유되어 있으므로 링크를 가진 사람은 누구나 접근 가능", "Allow anyone with the link to access": "링크가 있는 모든 사람에 대해 접근 허용", "Publish to internet": "인터넷에 게시", "Search engine indexing": "검색 엔진 인덱싱 허용", "Disable this setting to discourage search engines from indexing the page": "검색 엔진이 페이지를 인덱싱하지 못하도록 하려면 이 옵션을 비활성화 합니다", "Show last modified": "최종 수정일 표시", "Display the last modified timestamp on the shared page": "공유 페이지에 마지막으로 수정된 타임스탬프 표시", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "중첩된 문서들은 웹에 공유되지 않습니다. 공유 버튼을 토글해 액세스를 활성화하면, 미래에도 기본 동작으로 유지됩니다.", "{{ userName }} was added to the document": "{{ userName }} 이(가) 문서에 추가됨", "{{ count }} people added to the document": "{{ count }} 명이 문서에 추가됨", "{{ count }} people added to the document_plural": "{{ count }} 명이 문서에 추가됨", "{{ count }} groups added to the document": "{{ count }} 개의 그룹이 문서에 추가됨", "{{ count }} groups added to the document_plural": "{{ count }} 개의 그룹이 문서에 추가됨", "Logo": "로고", "Archived collections": "보관된 컬렉션", "New doc": "새 문서", "Empty": "비어 있음", "Collapse": "감추기", "Expand": "펼치기", "Document not supported – try Markdown, Plain text, HTML, or Word": "이 문서는 지원되지 않습니다 – Markdown, Plain Text, HTML이나 Word를 이용해주세요", "Go back": "돌아가기", "Go forward": "앞으로 가기", "Could not load shared documents": "공유 문서를 불러올 수 없습니다.", "Shared with me": "나에게 공유함", "Show more": "더 보기", "Could not load starred documents": "별표 표시한 문서를 불러올 수 없습니다", "Starred": "중요 문서", "Up to date": "최신 상태", "{{ releasesBehind }} versions behind": "{{ <PERSON><PERSON><PERSON><PERSON> }} 이전 버전", "{{ releasesBehind }} versions behind_plural": "{{ <PERSON><PERSON><PERSON><PERSON> }} 이전 버전", "Change permissions?": "권한을 변경할까요?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} 은(는) {{ parentDocumentName }} 내에서 이동할 수 없음", "You can't reorder documents in an alphabetically sorted collection": "알파벳순으로 정렬된 컬렉션에서는 문서를 재정렬할 수 없습니다.", "The {{ documentName }} cannot be moved here": "{{documentName}} 은(는) 여기로 이동할 수 없음", "Return to App": "앱으로 돌아가기", "Installation": "설치", "Unstar document": "문서 별표 해제", "Star document": "문서 별표 표시", "Template created, go ahead and customize it": "템플릿이 생성되었습니다. 커스터마이징이 가능합니다.", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "<em>{{titleWithDefault}}</em>에서 템플릿을 만들더라도 원본 문서는 수정되지 않습니다. 이 문서의 복사본을 생성해서 새 문서의 시작점으로 사용할 수 있는 템플릿을 만듭니다.", "Enable other members to use the template immediately": "다른 멤버가 템플릿을 즉시 사용할 수 있도록 설정", "Location": "위치", "Admins can manage the workspace and access billing.": "관리자는 워크스페이스를 관리하고 과금 기능에 접근할 수 있습니다.", "Editors can create, edit, and comment on documents.": "편집자는 문서를 만들고, 편집하고, 댓글을 작성할 수 있습니다.", "Viewers can only view and comment on documents.": "열람자는 문서 열람과 댓글 작성만 가능합니다.", "Are you sure you want to make {{ userName }} a {{ role }}?": "정말로 {{ userName }} 에게 {{ role }} 권한을 부여 하시겠습니까?", "I understand, delete": "이해했으며, 삭제합니다.", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "{{ userName }} 를 영구 삭제하시겠습니까? 이 작업은 복구할 수 없으므로 대신 사용자를 일시 정지하는 것이 좋습니다.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "{{ userName }} 계정 사용을 중지 하시겠습니까? 중지된 사용자는 로그인 할 수 없습니다.", "New name": "새 이름", "Name can't be empty": "이름은 비어 있을 수 없습니다.", "Check your email to verify the new address.": "새로운 주소를 확인하려면 이메일을 확인하세요.", "The email will be changed once verified.": "이메일은 확인 후 변경됩니다.", "You will receive an email to verify your new address. It must be unique in the workspace.": "새로운 주소를 확인하기 위한 이메일을 받게 됩니다. 이 주소는 작업 공간 내에서 고유해야 합니다.", "A confirmation email will be sent to the new address before it is changed.": "변경되기 전에 새로운 주소로 확인 이메일이 전송됩니다.", "New email": "새로운 이메일", "Email can't be empty": "이메일은 비워둘 수 없음", "Your import completed": "불러오기 완료됨", "Previous match": "이전 찾기", "Next match": "다음 찾기", "Find and replace": "찾아 바꾸기", "Find": "찾기", "Match case": "대소문자 일치", "Enable regex": "Regex 활성화", "Replace options": "바꾸기 옵션", "Replacement": "대체", "Replace": "바꾸기", "Replace all": "모두 바꾸기", "Profile picture": "프로필 사진", "Create a new doc": "새 문서 만들기", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} 은(는) 이 문서에 대한 액세스 권한이 없으므로 알림을 받지 않습니다.", "Keep as link": "링크로 유지", "Mention": "언급됨", "Embed": "내장", "Add column after": "뒤에 열 추가", "Add column before": "앞에 열 추가", "Add row after": "뒤에 행 추가", "Add row before": "앞에 행 추가", "Align center": "가운데 정렬", "Align left": "왼쪽 정렬", "Align right": "오른쪽 정렬", "Default width": "기본 너비", "Full width": "전체 너비", "Bulleted list": "글머리 기호 목록", "Todo list": "작업 목록", "Code block": "코드블럭", "Copied to clipboard": "클립보드에 복사되었습니다", "Code": "코드", "Comment": "댓글", "Create link": "링크 생성", "Sorry, an error occurred creating the link": "죄송합니다, 링크 생성 중 에러가 발생했습니다", "Create a new child doc": "새 하위 문서 생성", "Delete table": "테이블 삭제", "Delete file": "파일 삭제", "Width x Height": "너비 x 높이", "Download file": "파일 다운로드", "Replace file": "파일 바꾸기", "Delete image": "이미지 삭제", "Download image": "이미지 다운로드", "Replace image": "이미지 교체", "Italic": "이탤릭체", "Sorry, that link won’t work for this embed type": "죄송합니다, 해당 링크를 이 임베드 유형에서 사용할 수 없습니다", "File attachment": "첨부 파일", "Enter a link": "링크 입력", "Big heading": "큰 제목", "Medium heading": "중간 제목", "Small heading": "작은 제목", "Extra small heading": "아주 작은 제목", "Heading": "제목", "Divider": "구분선", "Image": "이미지", "Sorry, an error occurred uploading the file": "죄송합니다. 파일 업로드 중 에러가 발생했습니다", "Write a caption": "캡션 입력", "Info": "정보", "Info notice": "정보 공지", "Link": "링크", "Highlight": "강조", "Type '/' to insert": "삽입하려면 '/'를 입력하세요.", "Keep typing to filter": "찾으실 항목을 입력해 주세요", "Open link": "링크 열기", "Go to link": "링크로 이동", "Sorry, that type of link is not supported": "죄송합니다. 이런 종류의 링크는 아직 지원되지 않습니다.", "Ordered list": "번호가 있는 목록", "Page break": "페이지 나누기", "Paste a link": "링크 붙여 넣기", "Paste a {{service}} link…": "{{service}} 링크 붙여 넣기…", "Placeholder": "플레이스홀더", "Quote": "인용구", "Remove link": "링크 삭제", "Search or paste a link": "링크 검색 또는 붙여넣기", "Strikethrough": "취소선", "Bold": "굵게", "Subheading": "부제목", "Sort ascending": "오름차순 정렬", "Sort descending": "내림차순 정렬", "Table": "표", "Export as CSV": "CSV로 내보내기", "Toggle header": "토글 제목", "Math inline (LaTeX)": "인라인 수식 (LaTeX)", "Math block (LaTeX)": "수식 블록 (LaTeX)", "Merge cells": "셀 병합", "Split cell": "셀 나누기", "Tip": "팁", "Tip notice": "팁 공지", "Warning": "주의", "Warning notice": "주의 공지", "Success": "성공", "Success notice": "성공 안내", "Current date": "현재 날짜", "Current time": "현재 시간", "Current date and time": "현재 날짜 및 시간", "Indent": "들여쓰기", "Outdent": "내어쓰기", "Video": "동영상", "None": "없음", "Could not import file": "파일을 가져올 수 없음", "Unsubscribed from document": "문서 구독이 취소됨", "Unsubscribed from collection": "컬렉션 구독 취소", "Account": "계정", "API & Apps": "API 및 앱", "Details": "세부 정보", "Security": "보안", "Features": "실험적 기능", "Members": "멤버", "Groups": "그룹", "API Keys": "API 키", "Applications": "애플리케이션", "Shared Links": "공유한 링크", "Import": "불러오기", "Install": "설치", "Integrations": "연동", "Revoke token": "토큰 취소", "Revoke": "취소", "Show path to document": "문서 경로 표시", "Path to document": "문서 경로", "Group member options": "그룹 멤버 옵션", "Export collection": "컬렉션 내보내기", "Rename": "이름변경", "Sort in sidebar": "사이드바 정렬", "A-Z sort": "오름차순", "Z-A sort": "내림차순", "Manual sort": "수동 정렬", "Comment options": "댓글 옵션", "Show document menu": "문서 메뉴 표시", "{{ documentName }} restored": "{{ documentName }} 복원됨", "Document options": "문서 옵션", "Choose a collection": "컬렉션 선택", "Subscription inherited from collection": "컬렉션으로부터 상속된 구독", "Apply template": "템플릿 적용", "Enable embeds": "임베드 활성화", "Export options": "내보내기 옵션", "Group members": "그룹 구성원", "Edit group": "그룹 수정", "Delete group": "그룹 삭제", "Group options": "그룹 옵션", "Cancel": "취소", "Import menu options": "가져오기 메뉴 옵션", "Member options": "멤버 옵션", "New document in <em>{{ collectionName }}</em>": "<em>{{ collectionName }}</em> 컬렉션의 새 문서", "New child document": "새 하위 문서", "Save in workspace": "워크스페이스에 저장", "Notification settings": "알림 설정", "Revoke {{ appName }}": "{{ appName }} 취소", "Revoking": "취소", "Are you sure you want to revoke access?": "정말로 접근을 취소하시겠습니까?", "Delete app": "앱 삭제", "Revision options": "수정 옵션", "Share link revoked": "링크 공유가 취소되었습니다.", "Share link copied": "공유 할 링크가 복사되었습니다.", "Share options": "공유 옵션", "Go to document": "문서로 이동", "Revoke link": "링크 삭제", "Contents": "콘텐츠", "Headings you add to the document will appear here": "문서에 추가한 제목이 여기에 표시됩니다.", "Table of contents": "목차", "Change name": "이름 변경", "Change email": "이메일 변경", "Suspend user": "사용자 일시 정지", "An error occurred while sending the invite": "초대를 보내는 중 오류가 발생했습니다.", "User options": "사용자 옵션", "Change role": "역할 변경", "Resend invite": "초대 재전송", "Revoke invite": "초대 취소", "Activate user": "사용자 활성화", "template": "템플릿", "document": "문서", "published": "게시됨", "edited": "편집됨", "created the collection": "컬렉션 만들기", "mentioned you in": "당신을 언급했습니다", "left a comment on": "댓글을 남겼습니다", "resolved a comment on": "에 대한 댓글이 해결됨", "shared": "공유됨", "invited you to": "당신을 초대하였습니다:", "Choose a date": "날짜를 선택하세요", "API key created. Please copy the value now as it will not be shown again.": "API 키가 생성되었습니다. 지금 값을 복사해 두세요. 이후에는 다시 표시되지 않습니다.", "Scopes": "범위", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "공백으로 구분된 범위는 이 API 키의 액세스를 API의 특정 부분으로 제한합니다. 전체 액세스를 위해 비워두세요.", "Expiration": "만료", "Never expires": "만료 없음", "7 days": "7일", "30 days": "30일", "60 days": "60일", "90 days": "90일", "Custom": "사용자 지정", "No expiration": "만료 없음", "The document archive is empty at the moment.": "현재 보관 문서함이 비어 있습니다.", "Collection menu": "컬렉션 메뉴", "Drop documents to import": "가져올 문서 내려놓기", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> 컬렉션에 문서가 없습니다.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "액세스 권한이 있는 사용자 {{ usersCount }} 명 및 그룹 {{ groupsCount }} 개", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "액세스 권한이 있는 사용자 {{ usersCount }} 명 및 그룹 {{ groupsCount }} 개", "{{ usersCount }} users and a group have access": "액세스 권한이 있는 사용자 {{ usersCount }} 명 및 하나의 그룹", "{{ usersCount }} users and a group have access_plural": "액세스 권한이 있는 사용자 {{ usersCount }} 명 및 하나의 그룹", "{{ usersCount }} users with access": "액세스 권한이 있는 사용자 {{ usersCount }} 명", "{{ usersCount }} users with access_plural": "액세스 권한이 있는 사용자 {{ usersCount }} 명", "{{ groupsCount }} groups with access": "액세스 권한이 있는 그룹 {{ groupsCount }} 개", "{{ groupsCount }} groups with access_plural": "액세스 권한이 있는 그룹 {{ groupsCount }} 개", "Archived by {{userName}}": "{{userName}} 에 의해 보관됨", "Sorry, an error occurred saving the collection": "컬렉션을 저장하는 중에 오류가 발생했습니다.", "Add a description": "설명 추가", "Share": "공유하기", "Overview": "개요", "Recently updated": "최근에 업데이트됨", "Recently published": "최근에 게시됨", "Least recently updated": "오래된 순", "A–Z": "A-Z", "Signing in": "로그인 중", "You can safely close this window once the Outline desktop app has opened": "Outline desktop 앱이 열리면 이 창을 안전하게 닫을 수 있습니다.", "Error creating comment": "댓글을 만드는 중에 오류가 발생했습니다.", "Add a comment": "댓글 달기", "Add a reply": "답글 달기", "Reply": "답글", "Post": "게시하기", "Upload image": "사진 업로드", "No resolved comments": "해결된 댓글 없음", "No comments yet": "아직 댓글이 없습니다.", "New comments": "새 댓글", "Most recent": "최신 순", "Order in doc": "문서 내 정렬", "Resolved": "해결 됨", "Sort comments": "댓글 정렬", "Show {{ count }} reply": "{{ count }} 개의 답변 보기", "Show {{ count }} reply_plural": "{{ count }} 개의 답변 보기", "Error updating comment": "댓글을 업데이트하는 중에 오류가 발생했습니다.", "Document is too large": "문서가 너무 큽니다.", "This document has reached the maximum size and can no longer be edited": "이 문서는 최대 크기에 도달하여 더 이상 편집할 수 없습니다.", "Authentication failed": "인증 실패", "Please try logging out and back in again": "로그아웃했다가 다시 로그인해 보세요.", "Authorization failed": "사용자 인증 실패", "You may have lost access to this document, try reloading": "이 문서에 대한 액세스 권한을 잃었을 수 있습니다. 새로고침해 보세요.", "Too many users connected to document": "문서에 연결된 사용자가 너무 많습니다.", "Your edits will sync once other users leave the document": "다른 사용자가 문서를 떠나면 편집 내용이 동기화됩니다.", "Server connection lost": "서버 연결 끊김", "Edits you make will sync once you’re online": "온라인 상태가 되면 수정 사항이 동기화됩니다", "Document restored": "문서가 복원되었습니다", "Images are still uploading.\nAre you sure you want to discard them?": "이미지가 아직 업로드 중입니다.\n변경 내용을 삭제하시겠습니까?", "{{ count }} comment": "댓글 {{ count }} 개", "{{ count }} comment_plural": "댓글 {{ count }} 개", "Viewed by": "읽음", "only you": "나만", "person": "개인", "people": "명", "Last updated": "마지막 업데이트", "Type '/' to insert, or start writing…": "'/'를 입력하여 삽입하거나 쓰기 시작...", "Hide contents": "내용 숨기기", "Show contents": "내용 보기", "available when headings are added": "제목 추가 후 사용 가능", "Edit {{noun}}": "{{noun}} 수정하기", "Switch to dark": "어두운테마로 변경", "Switch to light": "밝은테마로 변경", "Archived": "보관됨", "Save draft": "임시 저장", "Done editing": "수정 완료", "Restore version": "버전 복원", "No history yet": "아직 기록이 없습니다", "Source": "원본", "Imported from {{ source }}": "{{ source }} 로부터 불러옴", "Stats": "통계", "{{ count }} minute read": "{{ count }} 분 읽기", "{{ count }} minute read_plural": "{{ count }} 분 읽기", "{{ count }} words": "{{ count }} 개 단어", "{{ count }} words_plural": "{{ count }} 개 단어", "{{ count }} characters": "{{ count }} 개 문자", "{{ count }} characters_plural": "{{ count }} 개 문자", "{{ number }} emoji": "{{ number }} 이모티콘", "No text selected": "선택된 텍스트 없음", "{{ count }} words selected": "{{ count }} 단어 선택됨", "{{ count }} words selected_plural": "{{ count }} 단어 선택됨", "{{ count }} characters selected": "{{ count }} 개의 문자가 선택됨", "{{ count }} characters selected_plural": "{{ count }} 개의 문자가 선택됨", "Contributors": "기여자", "Created": "생성됨", "Creator": "생성자", "Last edited": "마지막으로 수정됨", "Previously edited": "이전에 수정됨", "No one else has viewed yet": "아직 다른 사람이 보지 않았습니다.", "Viewed {{ count }} times by {{ teamMembers }} people": "{{ teamMembers }} 명에 의해 {{ count }} 회 조회됨", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "{{ teamMembers }} 명에 의해 {{ count }} 회 조회됨", "Viewer insights are disabled.": "열람자 정보가 비활성화되었습니다.", "Sorry, the last change could not be persisted – please reload the page": "죄송합니다. 마지막 변경 사항을 유지할 수 없습니다. 페이지를 새로고침하세요.", "{{ count }} days": "{{ count }} 일", "{{ count }} days_plural": "{{ count }} 일", "This template will be permanently deleted in <2></2> unless restored.": "이 템플릿은 복원하지 않으면 <2></2> 에서 영구적으로 삭제됩니다.", "This document will be permanently deleted in <2></2> unless restored.": "이 문서는 복원하지 않으면 <2></2> 에서 영구적으로 삭제됩니다.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "일부 텍스트를 강조 표시하고 <1></1> 컨트롤을 사용하여 새 문서를 만들 때 채울 수 있는 자리 표시자를 추가합니다.", "You’re editing a template": "템플릿을 수정 중입니다.", "Deleted by {{userName}}": "{{userName}} 에 의해 삭제됨", "Observing {{ userName }}": "{{ userName }} 관찰중", "Backlinks": "백링크", "Close": "닫기", "This document is large which may affect performance": "이 문서의 크기가 커서 성능에 영향을 줄 수 있음", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} 는 {{ appName }} 사용하여 문서를 공유하고 있습니다. 계속하려면 로그인하십시오.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "템플릿 <em>{{ documentTitle }}</em> 을 정말 삭제하시겠습니까?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "정말 <em>{{ documentTitle }}</em> 문서를 삭제하시겠습니까? 문서를 삭제하면 모든 이력이 삭제됩니다.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "정말 <em>{{ documentTitle }}</em> 문서를 삭제하시겠습니까? 문서를 삭제하면 모든 이력과 <em>{{ any }} 하위 문서들</em>이 삭제됩니다.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "정말 <em>{{ documentTitle }}</em> 문서를 삭제하시겠습니까? 문서를 삭제하면 모든 이력과 <em>{{ any }} 하위 문서들</em>이 삭제됩니다.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "나중에 {{noun}} 을 참조하거나 복원하는 옵션을 원한다면, 보관하는 것을 고려해보세요.", "Select a location to move": "이동할 위치 선택", "Document moved": "문서 이동 됨", "Couldn’t move the document, try again?": "문서를 이동 할 수 없습니다. 다시 시도하시겠습니까?", "Move to <em>{{ location }}</em>": "<em>{{ location }}</em>로 이동", "Couldn’t create the document, try again?": "문서를 만들 수 없습니다. 다시 시도하시겠습니까?", "Document permanently deleted": "문서가 영구적으로 삭제됨", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "<em>{{ documentTitle }}</em> 문서를 영구적으로 삭제하시겠습니까? 이 작업은 즉각적이며 취소할 수 없습니다.", "Select a location to publish": "발행할 위치 선택", "Document published": "문서 게시됨", "Couldn’t publish the document, try again?": "문서를 발행 할 수 없습니다. 다시 시도하시겠습니까?", "Publish in <em>{{ location }}</em>": "<em>{{ location }}</em>에 발행", "Search documents": "문서 검색", "No documents found for your filters.": "검색조건과 맞는 문서를 찾을 수 없습니다.", "You’ve not got any drafts at the moment.": "현재 임시보관 된 문서가 없습니다.", "Payment Required": "결제 필요", "No access to this doc": "이 문서에 접근할 수 없음", "It doesn’t look like you have permission to access this document.": "이 문서에 접근할 수 있는 권한이 없는 것 같습니다.", "Please request access from the document owner.": "문서 소유자에게 접근 권한을 요청하세요.", "Not found": "찾을 수 없음", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "찾고 있는 페이지를 찾을 수 없습니다. 삭제되었거나 링크가 올바르지 않을 수 있습니다.", "Offline": "오프라인", "We were unable to load the document while offline.": "오프라인 상태에서는 문서를 불러 올 수 없습니다.", "Your account has been suspended": "계정사용이 중지 되었습니다", "Warning Sign": "경고 표시", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "워크스페이스 관리자 (<em>{{ suspendedContactEmail }}</em>) (이)가 귀하의 계정 사용을 중지시켰습니다. 계정을 다시 활성화하려면 해당 관리자에게 직접 문의하십시오.", "Created by me": "내가 생성함", "Weird, this shouldn’t ever be empty": "이상하네, 여기가 비어 있으면 안 되는데", "You haven’t created any documents yet": "아직 문서를 만들지 않았습니다.", "Documents you’ve recently viewed will be here for easy access": "쉽게 액세스할 수 있도록 최근에 본 문서가 여기에 표시됩니다.", "We sent out your invites!": "초대장을 보냈습니다!", "Those email addresses are already invited": "해당 이메일 주소는 이미 초대되어 있습니다.", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "죄송합니다. 한 번에 {{MAX_INVITES}} 명만 초대할 수 있습니다.", "Invited {{roleName}} will receive access to": "초대된 {{roleName}} 은(는) 다음 접근 권한을 가집니다", "{{collectionCount}} collections": "콜렉션 {{collectionCount}}건", "Admin": "관리자", "Can manage all workspace settings": "모든 워크스페이스 설정을 관리할 수 있음", "Can create, edit, and delete documents": "문서를 생성, 수정, 삭제할 수 있음", "Can view and comment": "보고 코멘트를 남길 수 있음", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "워크스페이스에 사람들을 초대하세요. {{signinMethods}} 혹은 이메일 주소를 통해 가입할 수 있습니다.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "워크스페이스에 참여하도록 멤버를 초대합니다. {{signinMethods}} 으로 로그인해야 합니다.", "As an admin you can also <2>enable email sign-in</2>.": "관리자는 <2>이메일 로그인을 활성화</2>를 할 수 있습니다.", "Invite as": "초대:", "Role": "역할", "Email": "이메일", "Add another": "사용자 추가 초대", "Inviting": "초대 중", "Send Invites": "초대 보내기", "Open command menu": "명령어 메뉴 열기", "Forward": "전달", "Edit current document": "현재 문서 수정", "Move current document": "현재 문서로 이동", "Open document history": "문서 히스토리 열기", "Jump to search": "검색으로 이동", "Jump to home": "홈으로 이동", "Focus search input": "검색창에 포커스", "Open this guide": "이 가이드 열기", "Enter": "입력", "Publish document and exit": "문서를 저장하고 나가기", "Save document": "문서 저장", "Cancel editing": "변경 취소", "Collaboration": "협업", "Formatting": "포맷팅", "Paragraph": "단락", "Large header": "큰 제목", "Medium header": "중간 제목", "Small header": "작은 제목", "Underline": "밑줄", "Undo": "실행 취소", "Redo": "복원하기", "Lists": "목록", "Toggle task list item": "작업 목록 항목 전환", "Tab": "탭", "Indent list item": "목록 항목 들여쓰기", "Outdent list item": "목록 항목 내어쓰기", "Move list item up": "목록 항목을 위로 이동", "Move list item down": "목록 항목을 아래로 이동", "Tables": "표", "Insert row": "행 삽입", "Next cell": "다음 셀", "Previous cell": "이전 셀", "Space": "Space", "Numbered list": "번호 매기기 목록", "Blockquote": "블록 인용구", "Horizontal divider": "구분선", "LaTeX block": "LaTeX 블록", "Inline code": "인라인 코드", "Inline LaTeX": "인라인 LaTeX", "Triggers": "트리거", "Mention users and more": "사용자 멘션 및 기타", "Emoji": "이모지", "Insert block": "블록 삽입", "Sign In": "로그인", "Continue with Email": "이메일로 계속하기", "Continue with {{ authProviderName }}": "{{ authProviderName }} 사용하여 계속하기", "Back to home": "홈으로 돌아가기", "The workspace could not be found": "워크스페이스를 찾을 수 없습니다", "To continue, enter your workspace’s subdomain.": "계속하려면 워크스페이스의 서브도메인을 입력하세요.", "subdomain": "하위 도메인", "Continue": "계속", "The domain associated with your email address has not been allowed for this workspace.": "귀하의 이메일 주소와 연결된 도메인이 이 워크스페이스에 허용되지 않았습니다.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "로그인할 수 없습니다. 워크스페이스의 맞춤 URL로 이동한 다음 다시 로그인을 시도하세요.<1></1> 워크스페이스에 초대받은 경우 초대 이메일에서 해당 링크를 찾을 수 있습니다.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "죄송합니다. 개인 Gmail 주소로는 새 계정을 만들 수 없습니다.<1></1> 대신 Google Workspaces 계정을 사용하세요.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "당신의 사용자와 연관된 워크스페이스는 삭제가 예정되어 있으며 현재 접근할 수 없습니다.", "The workspace you authenticated with is not authorized on this installation. Try another?": "로그인한 워크스페이스는 이 설치 버전에서 승인되지 않았습니다. 다른 방법으로 시도하시겠습니까?", "We could not read the user info supplied by your identity provider.": "ID 공급자가 제공한 사용자 정보를 읽을 수 없습니다.", "Your account uses email sign-in, please sign-in with email to continue.": "귀하의 계정은 이메일 로그인을 사용합니다. 계속하려면 이메일로 로그인하십시오.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "이메일 로그인 링크가 최근에 전송되었습니다. 받은 편지함을 확인하거나 몇 분 후에 다시 시도하십시오.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "인증 실패 – 현재 로그인할 수 없습니다. 다시 시도해 주세요.", "Authentication failed – you do not have permission to access this workspace.": "인증 실패 – 이 워크스페이스에 액세스할 수 있는 권한이 없습니다.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "죄송합니다. 해당 로그인 링크가 더 이상 유효하지 않은 것 같습니다. 다른 링크를 요청해 보세요.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "귀하의 계정이 정지되었습니다. 계정을 다시 활성화하려면 워크스페이스 관리자에게 문의하세요.", "This workspace has been suspended. Please contact support to restore access.": "이 워크스페이스는 정지된 상태입니다. 지원에 연락해 접근 권한을 복구하세요.", "Authentication failed – this login method was disabled by a workspace admin.": "인증 실패 - 작업 공간 관리자가 이 로그인 방법을 비활성화했습니다.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "가입하려는 워크스페이스는 계정을 생성하기 전에 초대가 필요합니다.<1></1> 워크스페이스 관리자에게 초대를 요청하고 다시 시도하세요.", "Sorry, an unknown error occurred.": "죄송합니다. 알 수 없는 오류가 발생했습니다.", "Choose a workspace": "작업 공간 선택", "Choose an {{ appName }} workspace or login to continue connecting this app": "{{ appName }} 작업 공간을 선택하거나 로그인하여 이 앱 연결을 계속하세요.", "Create workspace": "워크스페이스 생성", "Setup your workspace by providing a name and details for admin login. You can change these later.": "관리자 로그인 이름과 세부 정보를 입력하여 워크스페이스를 설정하세요. 나중에 변경할 수 있습니다.", "Workspace name": "워크스페이스명", "Admin name": "관리자 이름", "Admin email": "관리자 이메일", "Login": "로그인", "Error": "오류", "Failed to load configuration.": "구성을 로드하지 못했습니다.", "Check the network requests and server logs for full details of the error.": "오류에 대한 자세한 내용은 네트워크 요청 및 서버 로그를 확인하십시오.", "Custom domain setup": "맞춤 도메인 설정", "Almost there": "거의 다 끝났습니다", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "사용자 지정 도메인이 성공적으로 Outline을 가리키고 있습니다. 설정 프로세스를 완료하려면 지원팀에 문의하세요.", "Choose workspace": "워크스페이스 선택", "This login method requires choosing your workspace to continue": "이 로그인 방법으로 계속하려면 워크스페이스를 선택해야 합니다.", "Check your email": "이메일을 확인하십시오", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "계정이 있는 경우 로그인 가능한 매직 링크가 이메일 <em>{{ emailLinkSentTo }}</em> 로 전송되었습니다.", "Back to login": "로그인으로 돌아가기", "Get started by choosing a sign-in method for your new workspace below…": "아래에서 새 워크스페이스의 로그인 방법을 선택하여 시작하세요.", "Login to {{ authProviderName }}": "{{ authProviderName }} 사용하여 계속하기", "You signed in with {{ authProviderName }} last time.": "최근 {{ authProviderName }} 으로 로그인했습니다.", "Or": "또는", "Already have an account? Go to <1>login</1>.": "이미 계정이 있으신가요? <1>로그인</1>.", "An error occurred": "오류 발생", "The OAuth client could not be found, please check the provided client ID": "OAuth 클라이언트를 찾을 수 없습니다. 제공된 클라이언트 ID를 확인하세요.", "The OAuth client could not be loaded, please check the redirect URI is valid": "OAuth 클라이언트를 로드할 수 없습니다. 리디렉션 URI가 유효한지 확인하세요.", "Required OAuth parameters are missing": "필수 OAuth 매개변수가 누락됨", "Authorize": "인증", "{{ appName }} wants to access {{ teamName }}": "{{ appName }}이(가) {{ teamName }}에 액세스하려고 합니다", "By <em>{{ developerName }}</em>": "<em>{{ developerName }}</em>님이 작성", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }}이(가) 귀하의 계정에 액세스하여 다음 작업을 수행할 수 있습니다", "read": "읽기", "write": "쓰기", "read and write": "읽기 및 쓰기", "API keys": "API 키", "attachments": "첨부", "collections": "컬렉션", "comments": "댓글", "documents": "문서", "events": "이벤트", "groups": "그룹", "integrations": "통합", "notifications": "알림", "reactions": "반응", "pins": "고정", "shares": "공유", "users": "사용자", "teams": "팀", "workspace": "작업 공간", "Read all data": "모든 데이터 읽기", "Write all data": "모든 데이터 쓰기", "Any collection": "모든 컬렉션", "All time": "모든 시간", "Past day": "어제", "Past week": "지난 주", "Past month": "지난 달", "Past year": "작년", "Any time": "모든 시간", "Remove document filter": "문서 검색 필터 제거", "Any status": "모든 상태", "Remove search": "검색 기록 제거", "Any author": "모든 작성자", "Search titles only": "제목에서만 찾기", "Something went wrong": "문제가 발생했습니다", "Please try again or contact support if the problem persists": "다시 한 번 시도해 보시고 문제가 계속된다면 문의해주세요", "No documents found for your search filters.": "검색조건과 맞는 문서를 찾을 수 없습니다.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "개인 API 키를 생성하여 API를 인증하고 작업 공간 데이터를 프로그래밍 방식으로 제어하세요.\n      자세한 내용은 <em>개발자 문서</em>를 참조하세요.", "API keys have been disabled by an admin for your account": "관리자가 귀하의 계정에 대한 API 키를 비활성화했습니다.", "Application access": "애플리케이션 액세스", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "{{ appName }} 계정에 대한 액세스 권한이 부여된 타사 및 내부 애플리케이션을 관리합니다.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API 키는 API와 인증하고 작업 공간의 데이터를 프로그래밍 방식으로 제어하는 데\n          사용될 수 있습니다. 자세한 내용은 <em>개발자 문서</em>를 참조하세요.", "Application published": "애플리케이션이 게시됨", "Application updated": "애플리케이션이 업데이트됨", "Client secret rotated": "클라이언트 시크릿이 교체됨", "Rotate secret": "시크릿 교체", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "클라이언트 시크릿을 교체하면 현재 시크릿이 무효화됩니다. 이 자격 증명을 사용하는 모든 애플리케이션을 업데이트해야 합니다.", "Displayed to users when authorizing": "사용자가 권한을 부여할 때 표시됨", "Developer information shown to users when authorizing": "사용자가 권한을 부여할 때 표시되는 개발자 정보", "Developer name": "개발자 이름", "Developer URL": "개발자 URL", "Allow users from other workspaces to authorize this app": "다른 작업 공간의 사용자가 이 앱을 승인하도록 허용", "Credentials": "자격 증명", "OAuth client ID": "OAuth 클라이언트 ID", "The public identifier for this app": "이 앱의 공개 식별자", "OAuth client secret": "OAuth 클라이언트 시크릿", "Store this value securely, do not expose it publicly": "이 값을 안전하게 저장하고 공개적으로 노출하지 마세요.", "Where users are redirected after authorizing this app": "이 앱을 승인한 후 사용자가 리디렉션되는 위치", "Authorization URL": "인증 URL", "Where users are redirected to authorize this app": "사용자가 이 앱을 승인하도록 리디렉션되는 위치", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "애플리케이션을 사용하면 Outline과 내부 또는 공개 통합을 구축하고 OAuth를 통해 보안 액세스를 제공할 수 있습니다. 자세한 내용은 <em>개발자 문서</em>를 참조하세요.", "by {{ name }}": "{{ name }} 에 의해", "Last used": "최근 사용됨", "No expiry": "만료 없음", "Restricted scope": "제한된 범위", "API key copied to clipboard": "API 키가 클립보드에 복사됨", "Copied": "복사됨", "Are you sure you want to revoke the {{ tokenName }} token?": "{{ tokenName }} 토큰을 취소하시겠습니까?", "Disconnect integration": "통합 해제", "Connected": "연결됨", "Disconnect": "연결 해제", "Disconnecting": "연결을 해제하는 중", "Allowed domains": "허용된 도메인", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "SSO를 사용하여 새 계정을 만들 수 있도록 허용해야 하는 도메인입니다. 이 설정을 변경해도 기존 사용자 계정에는 영향을 미치지 않습니다.", "Remove domain": "도메인 삭제", "Add a domain": "도메인 추가", "Save changes": "변경사항 저장", "Please choose a single file to import": "가져올 파일을 선택하십시오.", "Your import is being processed, you can safely leave this page": "가져오기를 처리 중입니다. 이 페이지를 종료해도 됩니다.", "File not supported – please upload a valid ZIP file": "지원되지 않는 파일 – 유효한 ZIP 파일을 업로드하십시오.", "Set the default permission level for collections created from the import": "가져오기로 생성된 컬렉션에 대한 기본 권한 수준 설정", "Uploading": "업로드 중", "Start import": "가져오기 시작", "Processing": "처리중", "Expired": "만료", "Completed": "완료", "Failed": "실패", "All collections": "모든 컬렉션", "Import deleted": "불러오기 삭제됨", "Export deleted": "내보내기 삭제됨", "Are you sure you want to delete this import?": "이 불러오기를 삭제하시겠습니까?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "가져온 문서를 삭제하면 가져온 내용으로 만든 모음 내용과 문서를 모두 삭제합니다. 이 동작은 되돌릴 수 없습니다.", "Check server logs for more details.": "더 많은 정보를 위해 서버 로그를 확인하세요", "{{userName}} requested": "{{userName}} 요청됨", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "그룹은 팀을 구성하기 위한 것입니다. 기능이나 책임(예: 지원 또는 엔지니어링) 을 중심으로 할 때 가장 잘 작동합니다.", "You’ll be able to add people to the group next.": "다음에 그룹에 사람들을 추가할 수 있습니다.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "그룹의 이름은 언제라도 수정할 수 있지만, 팀원들에게 혼란을 줄 수 있습니다.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "정말로 <em>{{groupName}}</em> 그룹을 삭제하시겠습니까? 그룹을 삭제하면 해당 구성원이 연결된 컬렉션 및 문서에 액세스할 수 없게 됩니다.", "Add people to {{groupName}}": "{{groupName}}에 사용자 추가", "{{userName}} was removed from the group": "{{userName}} 이(가) 그룹에서 제외되었습니다", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "<em>{{groupName}}</em> 그룹에서 멤버를 추가 및 제거합니다. 그룹에 멤버는 그룹이 가진 모든 컬렉션에 액세스할 수 있습니다.", "Add people": "사용자 추가하기", "Listing members of the <em>{{groupName}}</em> group.": "<em>{{groupName}}</em> 그룹의 구성원을 나열합니다.", "This group has no members.": "비어있는 그룹입니다.", "{{userName}} was added to the group": "{{userName}} 이(가) 그룹에 추가되었습니다", "Could not add user": "사용자를 추가할 수 없습니다", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "멤버를 그룹에 추가하여 그룹의 접근 권한을 부여할 수 있어요. 추가할 멤버가 있으신가요?", "Invite them to {{teamName}}": "{{team<PERSON>ame}}으로 초대해보세요", "Ask an admin to invite them first": "먼저 관리자에게 초대를 요청하세요", "Search by name": "이름으로 검색", "Search people": "사용자 검색", "No people matching your search": "찾으시는 사용자가 없습니다.", "No people left to add": "추가할 사용자가 없습니다", "Date created": "생성 일자", "Crop Image": "이미지 자르기", "Crop image": "이미지 자르기", "How does this work?": "어떻게 동작합니까?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "다른 인스턴스의 JSON 옵션에서 이전에 내보낸 압축 파일을 가져올 수 있습니다. {{ appName }} 에서 설정 사이드바에서 <em>내보내기</em>를 열고 <em>데이터 내보내기</em>클릭합니다.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "{{appName}}의 JSON 내보내기 옵션에서 zip 파일을 드래그 앤 드롭하거나 클릭하여 업로드", "Canceled": "취소됨", "Import canceled": "가져오기 취소됨", "Are you sure you want to cancel this import?": "가져오기를 취소 하시겠습니까?", "Canceling": "취소 중", "Canceling this import will discard any progress made. This cannot be undone.": "가져오기를 취소하면 진행 중인 내용은 버려지고, 되돌릴 수 없습니다.", "{{ count }} document imported": "문서 {{ count }}개 가져옴", "{{ count }} document imported_plural": "문서 {{ count }}개 가져옴", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "이전에 Outline 설치에서 내보낸 Zip 파일을 가져올 수 있습니다. 이는 컬렉션, 문서, 이미지를 가져옵니다. Outline에서 설정 사이드바의 <em>내보내기</em> 열고 <em>데이터 내보내기</em>클릭합니다.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "{{appName}} 의 <PERSON>son 내보내기 옵션에서 zip 파일을 드래그 앤 드롭하거나 클릭하여 업로드", "Configure": "구성", "Connect": "연결", "Last active": "최근 활동", "Guest": "게스트", "Never used": "사용한 적 없음", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "{{ appName }} 애플리케이션을 삭제하시겠습니까? 삭제 후에는 취소할 수 없습니다.", "Shared by": "~으로부터 공유됨:", "Date shared": "공유 날짜", "Last accessed": "마지막으로 액세스", "Domain": "도메인", "Views": "열람", "All roles": "모든 역할", "Admins": "관리자", "Editors": "에디터", "All status": "모든 상태", "Active": "활성", "Left": "왼쪽", "Right": "오른쪽", "Settings saved": "설정이 저장되었습니다", "Logo updated": "로고 업데이트됨", "Unable to upload new logo": "새 로고를 업로드할 수 없습니다.", "Delete workspace": "워크스페이스 삭제", "These settings affect the way that your workspace appears to everyone on the team.": "이러한 설정은 팀의 모든 사람에게 워크스페이스가 표시되는 방식에 영향을 줍니다.", "Display": "표시", "The logo is displayed at the top left of the application.": "로고는 애플리케이션의 왼쪽 상단에 표시됩니다.", "The workspace name, usually the same as your company name.": "워크스페이스 이름은 일반적으로 회사 이름과 동일합니다.", "Description": "설명", "A short description of your workspace.": "워크스페이스에 대한 간략한 설명입니다.", "Theme": "테마", "Customize the interface look and feel.": "인터페이스의 모양과 느낌을 사용자 지정합니다.", "Reset theme": "테마 초기화", "Accent color": "강조 색상", "Accent text color": "강조 텍스트 색상", "Public branding": "퍼블릭 브랜딩", "Show your workspace logo, description, and branding on publicly shared pages.": "공개로 공유된 페이지에 워크스페이스 로고, 설명 및 브랜딩을 표시합니다.", "Table of contents position": "목차 위치", "The side to display the table of contents in relation to the main content.": "본문에 관한 목차를 표시할 위치입니다.", "Behavior": "동작", "Subdomain": "하위 도메인", "Your workspace will be accessible at": "워크스페이스는 다음에서 액세스할 수 있습니다.", "Choose a subdomain to enable a login page just for your team.": "팀 전용 로그인 페이지를 활성화하려면 하위 도메인을 선택하십시오.", "This is the screen that workspace members will first see when they sign in.": "워크스페이스 멤버들이 로그인할 때 가장 먼저 보게 되는 화면입니다.", "Danger": "위험", "You can delete this entire workspace including collections, documents, and users.": "컬렉션, 문서, 사용자를 포함한 전체 워크스페이스를 삭제할 수 있습니다.", "Export data": "데이터 내보내기", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "전체 내보내기는 시간이 걸릴 수 있으므로 단일 문서나 컬렉션을 내보내는 것을 고려하세요. 내보내기가 시작되면 이 페이지를 떠나셔도 됩니다. 알림이 활성화되어 있는 경우 완료되면 <em>{{ userEmail }}</em>으로 링크를 이메일로 보내드립니다.", "Recent exports": "최근 내보내기", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "선택적 기능 및 실험적 기능을 관리합니다. 이 설정을 변경하면 워크스페이스에 모든 멤버에게 영향을 미칩니다.", "Separate editing": "분할 편집", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "활성화 시 문서는 항상 편집 가능한 대신 기본적으로 별도의 편집 모드를 갖습니다. 이 설정은 사용자 기본 설정에 따라 재정의될 수 있습니다.", "When enabled team members can add comments to documents.": "활성화되면 팀 구성원이 문서에 댓글을 달 수 있습니다.", "Create a group": "그룹 만들기", "Could not load groups": "그룹을 불러올 수 없습니다", "New group": "그룹 만들기", "Groups can be used to organize and manage the people on your team.": "그룹을 사용하여 팀의 사용자들을 구성하고 관리할 수 있습니다.", "No groups have been created yet": "아직 생성 된 그룹이 없습니다", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Markdown 문서의 zip 파일 가져오기(버전 0.67.0 이하에서 내보냄)", "Import data": "데이터 가져오기", "Import a JSON data file exported from another {{ appName }} instance": "다른 {{ appName }} 인스턴스에서 내보낸 JSON 데이터 파일 가져오기", "Import pages from a Confluence instance": "Confluence 인스턴스에서 페이지 가져오기", "Enterprise": "엔터프라이즈", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "기존 문서, 페이지 및 파일을 다른 도구 및 서비스에서 {{appName}} 으로 빠르게 전송합니다. HTML, Markdown 및 텍스트 문서를 앱의 컬렉션으로 직접 끌어다 놓을 수도 있습니다.", "Recent imports": "최근에 가져온", "Configure a variety of integrations with third-party services.": "다양한 타사 서비스와의 통합을 구성합니다.", "Could not load members": "멤버를 불러올 수 없습니다", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "{{appName}} 에 로그인한 모든 사람이 여기에 나열됩니다. {{signinMethods}} 을(를) 통해 접근한 비로그인 사용자가 있을 수 있습니다.", "Receive a notification whenever a new document is published": "새 문서가 게시될 때마다 알림을 받습니다", "Document updated": "문서 업데이트됨", "Receive a notification when a document you are subscribed to is edited": "작성한 문서가 수정되면 알림을 받습니다", "Comment posted": "댓글 작성됨", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "구독 중인 문서 또는 참여한 스레드가 댓글을 받으면 알림을 받습니다.", "Mentioned": "언급됨", "Receive a notification when someone mentions you in a document or comment": "다른 사람이 문서나 댓글에서 나를 언급하면 알림을 받습니다.", "Receive a notification when a comment thread you were involved in is resolved": "참여한 댓글 스레드가 해결되었을 때 알림을 받습니다.", "Collection created": "컬렉션 생성됨", "Receive a notification whenever a new collection is created": "새 컬렉션이 생성될 때마다 알림을 받습니다", "Invite accepted": "초대 수락됨", "Receive a notification when someone you invited creates an account": "내가 초대한 사람이 계정을 만들면 알림을 받습니다.", "Invited to document": "문서에 초대됨", "Receive a notification when a document is shared with you": "문서를 공유받으면 알림을 받습니다", "Invited to collection": "컬렉션에 초대됨", "Receive a notification when you are given access to a collection": "컬렉션에 대한 권한을 부여받으면 알림을 받습니다", "Export completed": "내보내기 완료", "Receive a notification when an export you requested has been completed": "요청한 내보내기가 완료되면 알림을 받습니다.", "Getting started": "시작하기", "Tips on getting started with features and functionality": "기능과 특징을 시작하기 위한 팁", "New features": "새로운 기능", "Receive an email when new features of note are added": "새로운 기능이 추가될 경우 이메일을 수신합니다", "Notifications saved": "알림이 저장되었습니다.", "Unsubscription successful. Your notification settings were updated": "구독 취소에 성공했습니다. 알림 설정이 업데이트되었습니다.", "Manage when and where you receive email notifications.": "이메일 알림을 받는 시기와 장소를 관리합니다.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "이메일 통합은 현재 비활성화되어 있습니다. 관련 환경 변수를 설정하고 서버를 다시 시작하여 알림을 활성화하십시오.", "Preferences saved": "설정 저장됨", "Delete account": "계정 삭제", "Manage settings that affect your personal experience.": "개인 경험에 영향을 미치는 설정을 관리합니다.", "Language": "언어", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "인터페이스 언어를 선택하십시오. 커뮤니티 번역은 <2>번역 포털</2> 을 통해 허용됩니다.", "Choose your preferred interface color scheme.": "선호하는 색상을 선택하세요.", "Use pointer cursor": "포인터 커서 사용", "Show a hand cursor when hovering over interactive elements.": "상호작용 요소 위로 마우스를 가져가면 손 모양의 커서를 표시합니다.", "Show line numbers": "줄 번호 표시", "Show line numbers on code blocks in documents.": "문서의 코드 블록에 줄 번호를 표시합니다.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "이 기능을 활성화하면, 문서에서 분할 편집 모드를 사용할 수 있습니다. 활성화하지 않으면 권한이 있을 경우에만 문서를 편집할 수 있습니다.", "Remember previous location": "이전 위치 기억", "Automatically return to the document you were last viewing when the app is re-opened.": "앱이 다시 열렸을때 마지막으로 보고 있던 문서로 돌아갑니다.", "Smart text replacements": "스마트 텍스트 대체", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "단축키를 기호, 대시, 스마트 따옴표 및 기타 타이포그래픽 요소로 교체하여 텍스트를 자동으로 형식화합니다.", "You may delete your account at any time, note that this is unrecoverable": "계정을 언제든지 삭제할 수 있지만 다시 복구할 수 없습니다", "Profile saved": "프로필이 저장되었습니다", "Profile picture updated": "프로필 사진이 업데이트 됨", "Unable to upload new profile picture": "새 프로필 사진을 업로드 할 수 없습니다", "Manage how you appear to other members of the workspace.": "워크스페이스의 다른 멤버들에게 어떻게 표시될지 관리합니다.", "Photo": "사진", "Choose a photo or image to represent yourself.": "당신을 표현하는 사진이나 그림을 선택하세요.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "이것은 당신의 실명일 수도, 닉네임일 수도 있습니다 — 남들에게 어떻게 언급되고 싶나요?", "Email address": "이메일 주소", "Are you sure you want to require invites?": "초대를 요구하시겠습니까?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "계정을 만들려면 먼저 새 사용자를 초대해야 합니다. <em>기본 역할</em> 및 <em>허용된 도메인</em> 은 더 이상 적용되지 않습니다.", "Settings that impact the access, security, and content of your workspace.": "접근, 보안, 워크스페이스 콘텐츠에 영향을 주는 설정입니다.", "Allow members to sign-in with {{ authProvider }}": "멤버가 {{ authProvider }} 으로 로그인하도록 허용", "Disabled": "비활성화됨", "Allow members to sign-in using their email address": "멤버가 이메일 주소를 사용하여 로그인하도록 허용", "The server must have SMTP configured to enable this setting": "이 설정을 사용하려면 서버에 SMTP가 구성되어 있어야 합니다.", "Access": "접근", "Allow users to send invites": "사용자가 초대를 보내도록 허용", "Allow editors to invite other people to the workspace": "편집자가 다른 인원을 워크스페이스에 초대하도록 허용", "Require invites": "초대 필요", "Require members to be invited to the workspace before they can create an account using SSO.": "멤버가 SSO를 사용하여 계정을 만들려면 워크스페이스에 초대해야 합니다.", "Default role": "기본 역할", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "새 계정의 기본 사용자 역할입니다. 이 설정을 변경해도 기존 사용자 계정에는 영향을 미치지 않습니다.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "활성화되면 워크스페이스에 모든 멤버가 문서를 인터넷에 공유 할 수 있습니다", "Viewer document exports": "뷰어 문서 내보내기", "When enabled, viewers can see download options for documents": "사용하도록 설정하면 보는 사람이 문서의 다운로드 옵션을 볼 수 있습니다.", "Users can delete account": "사용자는 계정을 삭제할 수 있음", "When enabled, users can delete their own account from the workspace": "활성화되면 사용자는 작업 공간에서 자신의 계정을 삭제할 수 있음", "Rich service embeds": "확장 임베딩", "Links to supported services are shown as rich embeds within your documents": "지원되는 서비스에 대한 링크는 문서 내에서 확장 임베딩으로 표시됩니다", "Collection creation": "컬렉션 생성됨", "Allow editors to create new collections within the workspace": "편집자가 워크스페이스에서 새 연결을 만들도록 허용", "Workspace creation": "워크스페이스 생성", "Allow editors to create new workspaces": "에디터들로 하여금 새로운 워크스페이스 생성을 허가", "Could not load shares": "공유를 불러올 수 없음", "Sharing is currently disabled.": "공유는 현재 비활성화되어 있습니다", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "<em>보안 설정</em>에서 인터넷 공유를 전역적으로 활성화 및 비활성화할 수 있습니다.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "공유된 문서는 다음과 같습니다. 공개 링크가 있는 사람은 링크가 삭제될 때까지 문서의 읽기 전용 버전에 액세스할 수 있습니다.", "You can create templates to help your team create consistent and accurate documentation.": "템플릿을 추가해서 팀이 일관성 있고 정확한 문서를 만들도록 도와줄 수 있습니다.", "Alphabetical": "알파벳 순", "There are no templates just yet.": "템플릿이 아직 없습니다.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "귀하의 이메일 주소로 확인 코드가 전송되었습니다. 워크스페이스를 영구적으로 제거하려면 아래에 코드를 입력하세요.", "Confirmation code": "확인 코드", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "워크스페이스 <1>{{workspaceName}}</1>를 지우면 모든 컬렉션, 문서, 유저, 관련된 데이터를 제거하고 즉시 {{appName}} 에서 로그아웃 됩니다.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "워크스페이스가 완전히 분리되어 있다는 것을 참고하세요. 각 워크스페이스는 다른 도메인, 설정, 사용자, 그리고 지불 설정을 가지고 있을 수 있습니다.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "현재 계정을 사용하여 새 워크스페이스를 만들고 있습니다 — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "다른 이메일로 워크스페이스를 생성하려면 홈페이지에서 가입을 시도하세요", "Trash emptied": "휴지통을 비웠습니다", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "휴지통의 모든 문서를 완전히 삭제하시겠습니까? 이 동작은 바로 실행하며 되돌릴 수 없습니다.", "Recently deleted": "최근 삭제됨", "Trash is empty at the moment.": "현재 휴지통이 비어 있습니다.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "귀하의 이메일 주소로 확인 코드가 전송되었습니다. 계정을 영구적으로 파기하려면 아래 코드를 입력하십시오.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "정말 계정을 삭제하시겠습니까? 계정을 삭제하면 계정과 연관된 식별 데이터가 삭제되며 되돌려질 수 없습니다. 계속할 경우 {{appName}} 에서 즉시 로그아웃되며 발급된 모든 API 토큰은 삭제됩니다.", "Delete my account": "계정 삭제", "Today": "오늘", "Yesterday": "어제", "Last week": "지난 주", "This month": "이번 달", "Last month": "지난 달", "This year": "올해", "Expired yesterday": "어제 만료됨", "Expired {{ date }}": "{{ date }} 에 만료됨", "Expires today": "오늘 만료", "Expires tomorrow": "내일 만료", "Expires {{ date }}": "{{ date }} 에 만료", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "죄송합니다. {{appName}} 을 워크스페이스에 연결하려면 GitHub에서 권한을 수락해야 합니다. 다시 시도하시겠습니까?", "Something went wrong while authenticating your request. Please try logging in again.": "요청을 인증하던 중 문제가 발생했습니다. 다시 로그인해 주세요.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "GitHub 계정의 소유자가 {{githubAppName}} GitHub 앱을 설치하도록 요청하였습니다. 승인되면 각각의 링크에 따른 미리보기가 표시됩니다.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "깃허브 오거나이제이션에 연결하거나 {appName} 개별 저장소에 연결하여 깃허브 이슈 미리보기와 풀 요청을 문서에서 활성화합니다.", "Enabled by {{integrationCreatedBy}}": "{{integrationCreatedBy}} 에 의해 활성화됨", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "연결을 해제하면 문서에서 이 조직의 GitHub 링크의 미리보기를 볼 수 없습니다. 계속하시겠습니까?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "GitHub 통합은 현재 비활성화되어 있습니다. 연관된 환경변수를 설정하고 서버를 다시 시작하여 통합을 활성화하세요.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Google 애널리틱스 4에 Measurement ID를 추가하여 작업공간에서 자신의 Google 애널리틱스 계정으로 문서 보기 및 분석을 전송하세요.", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Google 애널리틱스 관리 대시보드에서 \"Web\" 스트림을 만들고 생성된 코드 스니펫에서 Measurement ID를 복사하여 설치합니다.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "죄송합니다. {{appName}}을(를) 작업 공간에 연결하려면 Linear에서 권한을 수락해야 합니다. 다시 시도할까요?", "Something went wrong while processing your request. Please try again.": "요청을 처리하는 중에 문제가 발생했습니다. 다시 시도해 주세요.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "{appName}에 Linear 작업 공간을 연결하여 문서에서 Linear 문제의 미리 보기를 활성화합니다.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "연결을 끊으면 이 작업공간의 Linear 링크를 문서에서 미리보기할 수 없습니다. 정말로 계속할까요?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "Linear 통합이 현재 비활성화되어 있습니다. 관련 환경 변수를 설정하고 서버를 재시작하여 통합을 활성화하세요.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "조회수 및 분석 정보를 워크스페이스에서 당신의 Matomo 인스턴스로 보내도록 Matomo 설치를 구성하세요.", "Instance URL": "인스턴스 URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "Matomo 인스턴스의 URL입니다. Matomo Cloud를 사용하는 경우 matomo.cloud/로 끝납니다.", "Site ID": "사이트 ID", "An ID that uniquely identifies the website in your Matomo instance.": "<PERSON><PERSON> 인스턴스에서 웹사이트를 고유하게 식별하는 ID입니다.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "죄송합니다. {{ appName }} 을 워크스페이스에 연결하려면 Notion에서 권한을 수락해야 합니다. 다시 시도하시겠습니까?", "Import pages from Notion": "Notion에서 내보낸 페이지 가져오기", "Add to Slack": "Slack에 추가", "document published": "문서 게시됨", "document updated": "문서 업데이트됨", "Posting to the <em>{{ channelName }}</em> channel on": "<em>{{ channelName }}</em>의 채널에 게시", "These events should be posted to Slack": "이러한 이벤트는 Slack에 게시되어야 합니다.", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "이것은 향후 이 Slack 채널에 업데이트된 내용이 게시되는 것을 막을 것입니다. 계속하시겠습니까?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "죄송합니다. {{appName}} 을 워크스페이스에 연결하려면 Slack에서 권한을 수락해야 합니다. 다시 시도하시겠습니까?", "Personal account": "개인 계정", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "접근 권한을 가진 문서를 채팅에서 직접 검색 및 미리보기 하려면 Slack에 {{appName}} 계정을 연결하세요.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "개인 계정 연결을 해제하면 Slack에서 문서를 검색할 수 없습니다. 계속하시겠습니까?", "Slash command": "슬래시 명령어", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Slack에서 공유되는 {{ appName }} 링크에 대한 미리보기를 받고 <em>{{ command }}</em> 슬래시 명령을 사용하여 채팅에서 문서를 검색하세요.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "이것은 당신의 Slack 워크스페이스로부터 Outline 슬래시 명령어를 제거할 것입니다. 계속하시겠습니까?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "{{appName}} 컬렉션을 Slack 채널에 연결합니다. 문서가 게시되거나 업데이트되면 메시지가 자동으로 Slack에 게시됩니다.", "Comment by {{ author }} on \"{{ title }}\"": "{{ title }} 에 대한 {{ author }} 의 코멘트", "How to use {{ command }}": "{{ command }} 사용 방법", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "워크스페이스를 검색하려면 {{ command }} 를 사용하세요.\n{{ command2 }} 를 입력해 이 도움말을 표시하는 것을 도울 수 있습니다.", "Post to Channel": "채널에 게시하기", "This is what we found for \"{{ term }}\"": "\"{{ term }}\"에 대해 찾은 것입니다.", "No results for \"{{ term }}\"": "{{ term }} 에 대한 검색결과가 없습니다.", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "아직 Slack에 {{ appName }} 계정을 연동하지 않은 것 같습니다", "Link your account": "계정 연결하기", "Link your account in {{ appName }} settings to search from Slack": "Slack으로부터 검색하기 위하여 {{ appName }} 설정에서 계정을 연결하세요", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "작업 공간의 조회수 및 분석을 자신의 Umami 인스턴스로 전송하도록 Umami 설치를 구성합니다.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "Umami 인스턴스의 URL입니다. Umami Cloud를 사용하는 경우 {{ url }}로 시작합니다", "Script name": "스크립트 이름", "The name of the script file that Umami uses to track analytics.": "Umami가 분석을 추적하는 데 사용하는 스크립트 파일의 이름입니다.", "An ID that uniquely identifies the website in your Umami instance.": "Umami 인스턴스에서 웹사이트를 고유하게 식별하는 ID입니다.", "Are you sure you want to delete the {{ name }} webhook?": "{{ name }} 웹훅을 삭제하시겠습니까?", "Webhook updated": "웹훅 업데이트됨", "Update": "변경하기", "Updating": "변경 중", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "이 Webhook에 대한 설명이 포함된 이름과 일치하는 이벤트가 생성될 때 POST 요청을 보내야 하는 URL을 제공합니다.", "A memorable identifer": "기억 되는 식별자", "URL": "URL", "Signing secret": "비밀키 서명", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "모든 이벤트, 그룹 또는 개별 이벤트를 구독하십시오. 애플리케이션이 작동하는 데 필요한 최소한의 이벤트만 구독하는 것이 좋습니다.", "All events": "모든 이벤트", "All {{ groupName }} events": "모든 {{ groupName }} 이벤트", "Delete webhook": "웹훅 삭제", "Subscribed events": "구독한 이벤트", "Edit webhook": "웹훅 편집", "Webhook created": "웹훅 생성됨", "Webhooks": "웹훅", "New webhook": "새 웹훅", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "웹훅은 {{appName}} 에서 이벤트가 발생할 때 애플리케이션에 알리는 데 사용할 수 있습니다. 이벤트는 JSON 페이로드와 함께 https 요청으로 거의 실시간 전송됩니다.", "Inactive": "비활성화", "Create a webhook": "웹훅 만들기", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier는 {{appName}} 수천 개의 다른 비즈니스 도구와 쉽게 통합할 수 있는 플랫폼입니다. 워크플로를 자동화하고 데이터를 동기화하는 등의 작업을 수행하세요.", "Never logged in": "로그인 한 적 없음", "Online now": "현재 온라인", "Online {{ timeAgo }}": "{{ timeAgo }} 전에 온라인", "Viewed just now": "방금 확인함", "You updated {{ timeAgo }}": "{{ timeAgo }} 전에 내가 업데이트 함", "{{ user }} updated {{ timeAgo }}": "{{ user }} 업데이트 됨 {{ timeAgo }}", "You created {{ timeAgo }}": "{{ timeAgo }} 전에 내가 생성함", "{{ user }} created {{ timeAgo }}": "{{ user }} 이(가) {{ timeAgo }} 전에 생성", "Error loading data": "데이터 로딩 오류"}