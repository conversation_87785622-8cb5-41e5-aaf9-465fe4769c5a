{"New API key": "新 API 金鑰", "Open collection": "打開文件集", "New collection": "新文件集", "Create a collection": "建立文件集", "Edit": "編輯", "Edit collection": "編輯文件集", "Permissions": "權限", "Collection permissions": "文件集權限", "Share this collection": "分享這個文件集", "Search in collection": "在文件集中搜尋", "Star": "加入收藏", "Unstar": "取消收藏", "Subscribe": "訂閱", "Subscribed to document notifications": "訂閱文件通知", "Unsubscribe": "取消訂閱", "Unsubscribed from document notifications": "取消訂閱文件通知", "Archive": "封存", "Archive collection": "封存文件集", "Collection archived": "文件集已封存", "Archiving": "正在封存", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "封存此文件集將會一併封存其中的所有文件。已封存的文件將不再顯示於搜尋結果中。", "Restore": "還原", "Collection restored": "文件集已復原", "Delete": "刪除", "Delete collection": "刪除文件集", "New template": "新增範本", "Delete comment": "刪除評論", "Mark as resolved": "標記為已解決", "Thread resolved": "話題已解決", "Mark as unresolved": "標記為未解決", "View reactions": "查看迴響", "Reactions": "迴響", "Copy ID": "複製 ID", "Clear IndexedDB cache": "清除索引資料庫快取", "IndexedDB cache cleared": "索引資料庫快取已清除", "Toggle debug logging": "啟用除錯日誌", "Debug logging enabled": "除錯日誌已啟動", "Debug logging disabled": "偵錯記錄已停用", "Development": "開發", "Open document": "打開文件", "New document": "建立新文件", "New draft": "新增草稿", "New from template": "從範本建立", "New nested document": "建立新的子文件", "Publish": "發佈", "Published {{ documentName }}": "已發佈 {{ documentName }}", "Publish document": "發佈文件", "Unpublish": "下架", "Unpublished {{ documentName }}": "撤下發佈 {{ documentName }}", "Share this document": "分享這份文件", "HTML": "HTML", "PDF": "PDF", "Exporting": "正在匯出", "Markdown": "<PERSON><PERSON>", "Download": "下載", "Download document": "下載文件", "Copy as Markdown": "以Markdown格式複製", "Markdown copied to clipboard": "Markdown已複製到剪貼簿", "Copy as text": "以純文字複製", "Text copied to clipboard": "文字已複製至剪貼簿", "Copy public link": "複製公開連結", "Link copied to clipboard": "已經將連結複製到剪貼簿", "Copy link": "複製連結", "Copy": "複製", "Duplicate": "複製", "Duplicate document": "複製文件", "Copy document": "複製文件", "collection": "文件集", "Pin to {{collectionName}}": "釘選至 {{collectionName}}", "Pinned to collection": "釘選在文件集", "Pin to home": "釘選至首頁", "Pinned to home": "釘選至首頁", "Pin": "釘選", "Search in document": "搜尋文件", "Print": "列印", "Print document": "列印文件", "Import document": "匯入文件", "Templatize": "作為範本", "Create template": "建立範本", "Open random document": "隨機打開文件", "Search documents for \"{{searchQuery}}\"": "搜尋含有 “{{searchQuery}}” 的文件", "Move to workspace": "移至工作區", "Move": "移動", "Move to collection": "移至文件集", "Move {{ documentType }}": "移動 {{ documentType }}", "Are you sure you want to archive this document?": "您確定要封存這份文件？", "Document archived": "文件已被封存", "Archiving this document will remove it from the collection and search results.": "封存這個文件將從文件集及搜尋結果中移除。", "Delete {{ documentName }}": "刪除 {{ documentName }}", "Permanently delete": "永久刪除", "Permanently delete {{ documentName }}": "永久刪除 {{ documentName }}", "Empty trash": "清空回收桶", "Permanently delete documents in trash": "永久刪除回收桶裡的文件", "Comments": "留言", "History": "歷史紀錄", "Insights": "洞察分析", "Disable viewer insights": "停用瀏覽者洞察分析", "Enable viewer insights": "啟用瀏覽者洞察分析", "Leave document": "離開文件", "You have left the shared document": "您離開了共享的文件", "Could not leave document": "無法離開文件", "Home": "首頁", "Drafts": "草稿", "Search": "搜尋", "Trash": "垃圾桶", "Settings": "設定", "Profile": "個人資料", "Templates": "文件範本", "Notifications": "通知", "Preferences": "喜好設定", "Documentation": "文件", "API documentation": "API 文件", "Toggle sidebar": "切換側邊欄", "Send us feedback": "回饋意見給我們", "Report a bug": "回報錯誤", "Changelog": "更新日誌", "Keyboard shortcuts": "鍵盤快捷鍵", "Download {{ platform }} app": "下載 {{ platform }} 應用程式", "Log out": "登出", "Mark notifications as read": "標記所有通知為已讀", "Archive all notifications": "封存所有通知", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "已複製連結", "Dark": "深色", "Light": "淺色", "System": "依照系統", "Appearance": "介面設定", "Change theme": "變更主題", "Change theme to": "將主題變更為", "Switch workspace": "切換工作空間", "Select a workspace": "選擇工作空間", "New workspace": "新建的工作空間", "Create a workspace": "建立新的工作空間", "Login to workspace": "登入工作區", "Invite people": "邀請使用者", "Invite to workspace": "邀請至工作區", "Promote to {{ role }}": "提升為{{ role }}", "Demote to {{ role }}": "降級為{{ role }}", "Update role": "更新角色", "Delete user": "刪除使用者", "Collection": "文件集", "Collections": "文件集", "Debug": "除錯", "Document": "文件", "Documents": "所有文件", "Recently viewed": "最近瀏覽", "Revision": "版本紀錄", "Navigation": "導覽", "Notification": "通知", "People": "人員", "Workspace": "工作空間", "Recent searches": "最近搜尋", "currently editing": "正在編輯", "currently viewing": "正在瀏覽", "previously edited": "先前編輯過", "You": "您", "Viewers": "檢視者", "Collections are used to group documents and choose permissions": "文件集用於將文件分組並設定權限。", "Name": "名稱", "The default access for workspace members, you can share with more users or groups later.": "這是工作區成員的預設存取權，您之後可以再分享給更多使用者或群組。", "Public document sharing": "公開分享文件", "Allow documents within this collection to be shared publicly on the internet.": "允許此文件集內的文件在網際網路上公開分享。", "Commenting": "評論中", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "正在儲存", "Save": "儲存", "Creating": "正在建立", "Create": "建立", "Collection deleted": "文件集已刪除", "I’m sure – Delete": "我確定「刪除」", "Deleting": "正在刪除", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "您確定這麼做嗎？ 刪除 <em>{{collectionName}}</em> 文件集後將無法還原，而文件集中包含的文件會被移動到垃圾桶。", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "此外， <em>{{collectionName}}</em> 被用作開始視圖 - 刪除它會將開始頁面重置為主頁。", "Type a command or search": "輸入或搜尋指令", "Choose a template": "選擇範本", "Are you sure you want to permanently delete this entire comment thread?": "您確定要永久刪除整個留言串嗎？", "Are you sure you want to permanently delete this comment?": "您確定要永久刪除此留言嗎？", "Confirm": "確認", "manage access": "管理存取權限", "view and edit access": "可以檢視與編輯", "view only access": "僅供檢視", "no access": "無存取權限", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "您沒有權限將 {{ documentName }} 移動到 {{ collectionName }} 集合中", "Move document": "移動文件", "Moving": "正在移動", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "將文件<em>{{ title }}</em>移動至「{{ newCollectionName }}」文件集，會將所有工作區成員的權限從 「<em>{{ prevPermission }}</em>」 變更為 「 <em>{{ newPermission }}</em>」。", "Submenu": "子選單", "Collections could not be loaded, please reload the app": "文件集無法被載入，請重新整理 App", "Default collection": "預設文件集", "Start view": "初始介面", "Install now": "立即安裝", "Deleted Collection": "刪除的文件集", "Untitled": "無標題", "Unpin": "取消釘選", "{{ minutes }}m read": "「{{ minutes }}分鐘閱讀」", "Select a location to copy": "選擇要複製到的位置", "Document copied": "已複製文件", "Couldn’t copy the document, try again?": "無法複製文件，再嘗試一次？", "Include nested documents": "包含所有子文件", "Copy to <em>{{ location }}</em>": "複製到<em>{{ location }}</em>", "Search collections & documents": "搜尋文件集與文件", "No results found": "找不到任何結果", "New": "新", "Only visible to you": "只有您可以檢視", "Draft": "草稿", "Template": "範本", "You updated": "由您更新", "{{ userName }} updated": "由 {{ userName }} 更新", "You deleted": "你已刪除了", "{{ userName }} deleted": "已由 {{ userName }} 刪除", "You archived": "你已封存", "{{ userName }} archived": "由 {{ userName }} 封存", "Imported": "已匯入", "You created": "由您建立", "{{ userName }} created": "由 {{ userName }} 建立", "You published": "由您發佈", "{{ userName }} published": "由 {{ userName }} 發佈", "Never viewed": "從未瀏覽過", "Viewed": "已瀏覽", "in": "在", "nested document": "子文件", "nested document_plural": "子文件", "{{ total }} task": "{{ total }} 任務", "{{ total }} task_plural": "共 {{ total }} 項工作", "{{ completed }} task done": "{{ completed }} 任務已完成", "{{ completed }} task done_plural": "已完成 {{ completed }} 項工作", "{{ completed }} of {{ total }} tasks": "{{ completed }} / {{ total }} 任務已完成", "Currently editing": "正在編輯", "Currently viewing": "正在瀏覽", "Viewed {{ timeAgo }}": "{{ timeAgo }} 前瀏覽過", "Module failed to load": "模組載入失敗", "Loading Failed": "載入失敗", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "抱歉，部分應用程式載入失敗。有可能是因為在您打開分頁後被更新過，或是網路連線請求失敗。請嘗試重新載入。", "Reload": "重新載入", "Something Unexpected Happened": "發生了未預期的狀況", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "抱歉，發生了無法回復的錯誤 {{notified}}。錯誤有可能只是暫時的，請嘗試重新載入頁面。", "our engineers have been notified": "我們的工程師已經收到通知", "Show detail": "顯示詳情", "Revision deleted": "Revision deleted", "Current version": "目前版本", "{{userName}} edited": "{{userName}} 已編輯", "{{userName}} archived": "{{userName}} 將其封存", "{{userName}} restored": "{{userName}} 將其還原", "{{userName}} deleted": "{{userName}} 將其刪除", "{{userName}} added {{addedUserName}}": "{{userName}}新增了{{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}}刪除了{{removedUserName}}", "{{userName}} moved from trash": "{{userName}} 從垃圾桶移出", "{{userName}} published": "{{userName}} 已將其發布", "{{userName}} unpublished": "{{userName}} 已取消將其發布", "{{userName}} moved": "{{userName}} 將其移動", "Export started": "已經開始匯出", "Your file will be available in {{ location }} soon": "您的文件將很快將出現在 {{ location }} 中", "View": "檢視", "A ZIP file containing the images, and documents in the Markdown format.": "包含圖片和 Markdown 格式文件的 ZIP 文件。", "A ZIP file containing the images, and documents as HTML files.": "包含圖片和 HTML 格式文件的 ZIP 文件。", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "可用於將資料傳輸到另一個相容的 {{ appName }} 實例的結構化資料。", "Export": "匯出", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "匯出文件集 <em>{{collectionName}}</em> 可能需要一些時間。", "You will receive an email when it's complete.": "完成後，您將收到一封電子郵件。", "Include attachments": "包含附件", "Including uploaded images and files in the exported data": "在輸出的數據中將會包含您上傳的圖片跟文件", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "篩選器", "No results": "找不到任何結果", "{{authorName}} created <3></3>": "{{authorName}}建立了<3></3>", "{{authorName}} opened <3></3>": "{{authorName}}開啟了<3></3>", "Search emoji": "搜尋表情符號", "Search icons": "搜尋圖示", "Choose default skin tone": "選擇預設的膚色", "Show menu": "顯示選單", "Icon Picker": "圖示選擇器", "Icons": "圖示", "Emojis": "表情符號", "Remove": "移除", "All": "全部", "Frequently Used": "經常使用的", "Search Results": "搜尋結果", "Smileys & People": "表情符號與人物", "Animals & Nature": "動物與自然", "Food & Drink": "食物和飲料", "Activity": "活動", "Travel & Places": "旅遊與地點", "Objects": "物品", "Symbols": "符號", "Flags": "旗幟", "Select a color": "選擇顏色", "Loading": "正在讀取", "Permission": "權限", "View only": "只可檢視", "Can edit": "可以編輯", "No access": "無存取權限", "Default access": "預設存取權限", "Change Language": "變更語言", "Dismiss": "知道了", "You’re offline.": "您目前離線中。", "Sorry, an error occurred.": "抱歉，發生錯誤。", "Click to retry": "按此重新嘗試", "Back": "上一步", "Unknown": "未知", "Mark all as read": "全部標記為已讀", "You're all caught up": "已看完所有評論！", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "已發佈", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }}發佈了{{ emoji }}迴響", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }}及{{ secondUsername }}發佈了{{ emoji }}迴響", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }}及{{ count }}人發佈了{{ emoji }}迴響", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }}及{{ count }}人發佈了{{ emoji }}迴響", "Add reaction": "新增迴響", "Reaction picker": "選擇迴響", "Could not load reactions": "無法載入迴響", "Reaction": "迴響", "Results": "結果", "No results for {{query}}": "沒有任何匹配 ”{{query}}“ 的搜尋結果", "Manage": "管理", "All members": "所有成員", "Everyone in the workspace": "工作區裡的所有人", "{{ count }} member": "{{ count }} 位成員", "{{ count }} member_plural": "{{ count }} 位成員", "Invite": "邀請", "{{ userName }} was added to the collection": "{{ userName }} 已經被新增到文件集", "{{ count }} people added to the collection": "{{ count }} 人已經被新增到文件集", "{{ count }} people added to the collection_plural": "{{ count }} 人已經被新增到文件集", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} 人以及 {{ count2 }} 個群組已經被新增到文件集", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} 人以及 {{ count2 }} 個群組已經被新增到文件集", "Add": "新增", "Add or invite": "新增或邀請", "Viewer": "檢視者", "Editor": "編輯者", "Suggestions for invitation": "建議邀請", "No matches": "無匹配", "Can view": "可以查看", "Everyone in the collection": "文件集裡的所有人", "You have full access": "你擁有完整權限", "Created the document": "建立文件", "Other people": "其他人", "Other workspace members may have access": "其他工作區的成員可能可以存取", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "此文件可能透過您無權存取的上層文件或文件集，與更多工作區成員共用。", "Access inherited from collection": "從文件集繼承的存取權", "{{ userName }} was removed from the document": "{{ userName }} 已經從文件中移除", "Could not remove user": "無法移除使用者", "Permissions for {{ userName }} updated": "{{ userName }} 的權限已更新", "Could not update user": "無法更新使用者資料", "Has access through <2>parent</2>": "透過<2>上層文件</2>取得存取權", "Suspended": "已停用", "Invited": "已邀請", "Active <1></1> ago": "最後活動於 <1></1> 前", "Never signed in": "從未登入", "Leave": "離開", "Only lowercase letters, digits and dashes allowed": "只允許使用小寫字母、數字和破折號", "Sorry, this link has already been used": "抱歉，此連結已被使用", "Public link copied to clipboard": "已將連結複製到剪貼簿", "Web": "網頁", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "任何擁有連結的人都可以存取，因為父文件 <2>{{ documentTitle}}</2> 是共享的。", "Allow anyone with the link to access": "允許任何知道該連結的人訪問", "Publish to internet": "發佈到互聯網", "Search engine indexing": "搜索引擎索引", "Disable this setting to discourage search engines from indexing the page": "停用此設定可阻止搜尋引擎為此頁面建立索引。", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "巢狀文件不會在網路上共用。切換共享以啟用訪問，這會成為未來的預設行為", "{{ userName }} was added to the document": "{{ userName }} 已新增至此文件", "{{ count }} people added to the document": "{{ count }} 人已新增至此文件", "{{ count }} people added to the document_plural": "{{ count }} 人已新增至此文件", "{{ count }} groups added to the document": "{{ count }} 個團隊已新增至此文件", "{{ count }} groups added to the document_plural": "{{ count }} 個團隊已新增至此文件", "Logo": "Logo", "Archived collections": "已封存的文件集", "New doc": "新文件", "Empty": "空無一物", "Collapse": "收合", "Expand": "展開", "Document not supported – try Markdown, Plain text, HTML, or Word": "不支援的文件格式－請嘗試使用 Markdown、純文字、HTML 或 Word 格式", "Go back": "返回", "Go forward": "前進", "Could not load shared documents": "無法載入分享文件", "Shared with me": "與我共用", "Show more": "顯示更多", "Could not load starred documents": "無法加載已標記文件", "Starred": "已收藏", "Up to date": "已是最新版本", "{{ releasesBehind }} versions behind": "{{ <PERSON><PERSON><PERSON><PERSON> }} 版本落後", "{{ releasesBehind }} versions behind_plural": "{{ releases<PERSON>eh<PERSON> }} 數個版本落後", "Change permissions?": "變更權限？", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "「{{ documentName }}」無法在「{{ parentDocumentName }}」內移動", "You can't reorder documents in an alphabetically sorted collection": "您無法對按字母順序排序的文件集中的文件再重新排序", "The {{ documentName }} cannot be moved here": "「{{ documentName }}」無法移動至此。", "Return to App": "返回", "Installation": "安裝", "Unstar document": "取消星號", "Star document": "標示星號", "Template created, go ahead and customize it": "範本已經建立，繼續將其客製化", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "從 <em>{{titleWithDefault}}</em> 建立範本是不會對原本的文件造成影響－我們會將文件複製後並將其轉換為可以用來建立新文件的範本。", "Enable other members to use the template immediately": "允許其它成員立即使用此範本", "Location": "位置", "Admins can manage the workspace and access billing.": "管理員可以管理工作區和帳單資訊", "Editors can create, edit, and comment on documents.": "編輯者可以建立、編輯、以及在文件中評論", "Viewers can only view and comment on documents.": "預覽者只能預覽文件與留言", "Are you sure you want to make {{ userName }} a {{ role }}?": "您確定要將 {{ userName }} 設定為 {{ role }} 嗎？", "I understand, delete": "我明白了，刪除", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "您確定要永久刪除 {{ userName }} 嗎？此操作不可恢復，請考慮使用暫停用戶。", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "您確定要停用 {{ userName }} 的賬號嗎？遭到停用的使用者將無法登入。", "New name": "新的名稱", "Name can't be empty": "名稱不可以空白", "Check your email to verify the new address.": "檢查你的電子郵件以驗證新地址。", "The email will be changed once verified.": "電子郵件一旦經過驗證就會被更改。", "You will receive an email to verify your new address. It must be unique in the workspace.": "您將收到一封電子郵件以驗證您的新地址。它在工作空間中必須是唯一的。", "A confirmation email will be sent to the new address before it is changed.": "在更改新地址之前，將會向新地址發送一封確認電子郵件。", "New email": "新電子郵件", "Email can't be empty": "電子郵件不能留空", "Your import completed": "您的匯入已完成", "Previous match": "之前的匹配結果", "Next match": "下一筆", "Find and replace": "尋找與取代", "Find": "搜尋", "Match case": "區分大小寫", "Enable regex": "使用正規表示式（regex）", "Replace options": "替代方式", "Replacement": "替代", "Replace": "取代", "Replace all": "全部取代", "Profile picture": "大頭貼", "Create a new doc": "建立新文件", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }}不會被通知，因為他們沒有這個文件的存取權", "Keep as link": "保留為連結", "Mention": "Mention", "Embed": "嵌入", "Add column after": "在後面增加一欄", "Add column before": "在前面增加一欄", "Add row after": "在後面增加一列", "Add row before": "在前面增加一列", "Align center": "置中對齊", "Align left": "靠左對齊", "Align right": "靠右對齊", "Default width": "預設寬度：", "Full width": "全螢幕寬度", "Bulleted list": "項目清單", "Todo list": "工作清單", "Code block": "程式碼區塊", "Copied to clipboard": "已複製到剪貼簿", "Code": "程式碼", "Comment": "評論", "Create link": "建立超連結", "Sorry, an error occurred creating the link": "抱歉，建立分享連結時發生錯誤", "Create a new child doc": "建立子文件", "Delete table": "刪除表格", "Delete file": "刪除檔案", "Width x Height": "Width x Height", "Download file": "下載檔案", "Replace file": "取代檔案", "Delete image": "刪除圖片", "Download image": "下載圖片", "Replace image": "替換圖片", "Italic": "斜體", "Sorry, that link won’t work for this embed type": "抱歉，不支援將這個連結嵌入到文件", "File attachment": "附加檔案", "Enter a link": "輸入一個連結", "Big heading": "大標題", "Medium heading": "中標題", "Small heading": "小標題", "Extra small heading": "更小的標題", "Heading": "標題", "Divider": "分隔線", "Image": "圖片", "Sorry, an error occurred uploading the file": "抱歉，上傳檔案時發生錯誤", "Write a caption": "撰寫說明文字", "Info": "資訊", "Info notice": "訊息通知", "Link": "連結", "Highlight": "強調", "Type '/' to insert": "輸入 '/' 進行插入", "Keep typing to filter": "繼續輸入以進行篩選", "Open link": "開啟連結", "Go to link": "前往連結", "Sorry, that type of link is not supported": "抱歉，尚未支援此類型的連結", "Ordered list": "有序清單", "Page break": "分頁符號", "Paste a link": "貼上連結", "Paste a {{service}} link…": "貼上 {{service}} 連結...", "Placeholder": "占位符", "Quote": "引用", "Remove link": "移除連結", "Search or paste a link": "搜尋或貼上網址", "Strikethrough": "刪除線", "Bold": "粗體", "Subheading": "副標題", "Sort ascending": "由小到大排序", "Sort descending": "由大到小排序", "Table": "表格", "Export as CSV": "匯出為CSV", "Toggle header": "切換標題列", "Math inline (LaTeX)": "行內數學 (LaTeX)", "Math block (LaTeX)": "數學區塊 (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "提示", "Tip notice": "小幫手", "Warning": "警告", "Warning notice": "警告", "Success": "成功", "Success notice": "成功通知", "Current date": "當前日期", "Current time": "當前時間", "Current date and time": "當前日期和時間", "Indent": "縮排", "Outdent": "凸排", "Video": "影片", "None": "無", "Could not import file": "無法匯入檔案", "Unsubscribed from document": "取消訂閱文件", "Unsubscribed from collection": "取消訂閱文件", "Account": "帳號", "API & Apps": "API & Apps", "Details": "詳細", "Security": "安全性", "Features": "功能特色", "Members": "成員", "Groups": "群組", "API Keys": "API金鑰", "Applications": "Applications", "Shared Links": "已分享的連結", "Import": "匯入", "Install": "Install", "Integrations": "整合", "Revoke token": "撤銷權杖", "Revoke": "撤銷", "Show path to document": "顯示文件路徑", "Path to document": "文件路徑", "Group member options": "群組成員選項", "Export collection": "匯出文件集", "Rename": "重新命名", "Sort in sidebar": "在側邊欄中排序", "A-Z sort": "A-Z排列", "Z-A sort": "Z-A排列", "Manual sort": "手動排序", "Comment options": "評論選項", "Show document menu": "顯示文件目錄", "{{ documentName }} restored": "{{ documentName }} 已還原", "Document options": "文件選項", "Choose a collection": "選擇一個文件集", "Subscription inherited from collection": "訂閱繼承自集合", "Apply template": "Apply template", "Enable embeds": "啟用嵌入物件", "Export options": "匯出選項", "Group members": "群組成員", "Edit group": "編輯群組", "Delete group": "刪除群組", "Group options": "群組選項", "Cancel": "取消", "Import menu options": "Import menu options", "Member options": "成員選項", "New document in <em>{{ collectionName }}</em>": "新文件在<em>{{ collectionName }}</em>", "New child document": "建立新的子文件", "Save in workspace": "儲存在工作區", "Notification settings": "通知設定", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "撤銷中", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "修訂版本選項", "Share link revoked": "分享連結已註銷", "Share link copied": "分享網址已複製", "Share options": "共享設定", "Go to document": "跳轉到文件", "Revoke link": "註銷連結", "Contents": "目錄", "Headings you add to the document will appear here": "您在文件中新增的標題會顯示在此", "Table of contents": "目錄", "Change name": "變更名稱", "Change email": "更改電子郵件", "Suspend user": "停用使用者", "An error occurred while sending the invite": "發送邀請時發生錯誤", "User options": "使用者選項", "Change role": "變更角色", "Resend invite": "重新發送邀請", "Revoke invite": "取消邀請", "Activate user": "啟用使用者", "template": "範本", "document": "文件", "published": "已發佈", "edited": "已編輯", "created the collection": "建立文件集", "mentioned you in": "提到了您:", "left a comment on": "發表評論", "resolved a comment on": "標記評論為已解決", "shared": "已分享", "invited you to": "邀請您到", "Choose a date": "選擇日期", "API key created. Please copy the value now as it will not be shown again.": "API 金鑰已建立。請立即複製該值，因為它不會再次顯示。", "Scopes": "範圍", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "以空格分隔的作用域會將此 API 金鑰的訪問權限限制在 API 的特定部分。若要全權訪問，請留空。", "Expiration": "到期日", "Never expires": "永不過期", "7 days": "7 天", "30 days": "30 天", "60 days": "60 天", "90 days": "90 天", "Custom": "自訂", "No expiration": "沒有期限", "The document archive is empty at the moment.": "目前沒有任何文件被封存。", "Collection menu": "文件集選單", "Drop documents to import": "拖曳文件以進行匯入", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> 中還沒有任何文件。", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} 位使用者和 {{ groupsCount }} 群組具有存取權限", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} 位使用者和 {{ groupsCount }} 群組具有存取權限", "{{ usersCount }} users and a group have access": "{{ usersCount }} 個使用者和一個群組有權限訪問", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} 個使用者和群組有權限訪問", "{{ usersCount }} users with access": "{{ usersCount }} 個使用者擁有存取權限", "{{ usersCount }} users with access_plural": "{{ usersCount }} 個使用者擁有存取權限", "{{ groupsCount }} groups with access": "{{ groupsCount }} 個群組擁有存取權限", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} 個群組擁有存取權限", "Archived by {{userName}}": "已由 {{userName}} 封存", "Sorry, an error occurred saving the collection": "抱歉，儲存到文件集時發生了錯誤。", "Add a description": "新增描述", "Share": "分享", "Overview": "概覽", "Recently updated": "最近更新", "Recently published": "最近發布", "Least recently updated": "很久以前更新", "A–Z": "A-Z", "Signing in": "正在登入", "You can safely close this window once the Outline desktop app has opened": "Outline 桌面應用程式打開後，您可以安全地關閉此視窗", "Error creating comment": "留言時發生錯誤", "Add a comment": "新增留言", "Add a reply": "新增回覆…", "Reply": "回覆", "Post": "發佈", "Upload image": "上傳圖片", "No resolved comments": "無已解決的留言", "No comments yet": "尚無留言", "New comments": "新評論", "Most recent": "最近", "Order in doc": "文件中的順序", "Resolved": "已解決", "Sort comments": "對評論進行排序", "Show {{ count }} reply": "顯示 {{ count }} 則回覆", "Show {{ count }} reply_plural": "顯示{{ count }}則回覆。", "Error updating comment": "無法更新評論", "Document is too large": "文件大小太大", "This document has reached the maximum size and can no longer be edited": "該文檔已達到最大字數限制，無法再進行編輯。", "Authentication failed": "認證失敗", "Please try logging out and back in again": "請嘗試登出並再次登入", "Authorization failed": "驗證失敗", "You may have lost access to this document, try reloading": "您可能已失去對此文件的訪問權限，請嘗試重新加載。", "Too many users connected to document": "太多用戶正在使用這個文檔", "Your edits will sync once other users leave the document": "等其他用戶離開這個文檔之後，您的修改將會被保存", "Server connection lost": "與伺服器連線中斷", "Edits you make will sync once you’re online": "您所做的變更將在您與伺服器連線時同步", "Document restored": "文件已經被還原", "Images are still uploading.\nAre you sure you want to discard them?": "圖片皆已被上傳。\n您確定要捨棄嗎？", "{{ count }} comment": "{{ count }} 則評論", "{{ count }} comment_plural": "{{ count }} 則評論", "Viewed by": "已瀏覽過", "only you": "僅限自己", "person": "人", "people": "人", "Last updated": "最後更新於", "Type '/' to insert, or start writing…": "輸入 '/' 以插入，或直接撰寫…", "Hide contents": "隱藏目錄內容", "Show contents": "顯示目錄內容", "available when headings are added": "新增標題後有效", "Edit {{noun}}": "編輯 {{noun}}", "Switch to dark": "切換到深色模式", "Switch to light": "切換到淺色模式", "Archived": "已封存", "Save draft": "儲存草稿", "Done editing": "編輯完成", "Restore version": "還原版本", "No history yet": "暫無歷史紀錄", "Source": "來源", "Imported from {{ source }}": "從 {{ source }} 匯入", "Stats": "統計", "{{ count }} minute read": "{{ count }} 分鐘閲讀時長", "{{ count }} minute read_plural": "{{ count }} 分鐘閲讀時長", "{{ count }} words": "{{ count }} 字", "{{ count }} words_plural": "{{ count }} 字", "{{ count }} characters": "{{ count }} 字", "{{ count }} characters_plural": "{{ count }} 字", "{{ number }} emoji": "{{ number }} 個表情符號", "No text selected": "尚未選擇任何文字", "{{ count }} words selected": "已選擇 {{ count }} 個字", "{{ count }} words selected_plural": "已選擇 {{ count }} 個字", "{{ count }} characters selected": "已選擇 {{ count }} 個字", "{{ count }} characters selected_plural": "已選擇 {{ count }} 個字", "Contributors": "貢獻者", "Created": "建立日期", "Creator": "建立者", "Last edited": "最後編輯於", "Previously edited": "先前編輯過", "No one else has viewed yet": "尚未有人查看", "Viewed {{ count }} times by {{ teamMembers }} people": "{{ teamMembers }} 人共瀏覽過 {{ count }} 次", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "{{ teamMembers }} 人共瀏覽過 {{ count }} 次", "Viewer insights are disabled.": "檢視者洞察功能已停用。", "Sorry, the last change could not be persisted – please reload the page": "抱歉，無法保留上次更改 - 請重新加載頁面", "{{ count }} days": "{{ count }} 天", "{{ count }} days_plural": "{{ count }} 天", "This template will be permanently deleted in <2></2> unless restored.": "範本若未於 <2></2> 還原，將被永久刪除。", "This document will be permanently deleted in <2></2> unless restored.": "文件若未於 <2></2> 還原，將被永久刪除。", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "高亮顯示一些文字並使用 <1></1> 控制項添加可以在建立新文件時填寫的佔位符", "You’re editing a template": "您正在編輯範本", "Deleted by {{userName}}": "已由 {{userName}} 刪除", "Observing {{ userName }}": "正在觀察 {{ userName }}", "Backlinks": "反向連結", "Close": "關閉", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} 正在使用 {{ appName }} 分享文件，請登入以繼續。", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "您確定要刪除 <em>{{ documentTitle }}</em> 範本嗎？", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "你確定嗎？刪除 <em>{{ documentTitle }}</em> 文件將刪除其所有歷史記錄</em>。", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "您確定要這麼做嗎？將文件 <em>{{ documentTitle }}</em> 刪除同時也會刪除所有它的歷史紀錄以及 <em>{{ any }} 所屬的子文件</em>。", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "您確定要這麼做嗎？將文件 <em>{{ documentTitle }}</em> 刪除同時也會刪除所有它的歷史紀錄以及 <em>{{ any }} 所屬的子文件</em>。", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "如果您希望將來可以引用或還原 {{noun}}，請考慮改將其封存。", "Select a location to move": "選擇要移動至的位置", "Document moved": "文件已移動", "Couldn’t move the document, try again?": "無法移動文件，請重新嘗試。", "Move to <em>{{ location }}</em>": "移動至 <em>{{ location }}</em>", "Couldn’t create the document, try again?": "無法建立文件，請再試一次？", "Document permanently deleted": "文件已經被永久刪除", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "您確定要永久刪除文件 <em>{{ documentTitle }}</em> 嗎？這個動作會立即生效且無法被回復。", "Select a location to publish": "選擇要發佈到的位置", "Document published": "文件已發佈", "Couldn’t publish the document, try again?": "無法發佈文件，請重新嘗試。", "Publish in <em>{{ location }}</em>": "發佈到 <em>{{ location }}</em>", "Search documents": "搜尋文件", "No documents found for your filters.": "沒有符合過篩選條件的文件。", "You’ve not got any drafts at the moment.": "目前您還沒有任何草稿。", "Payment Required": "需要付款", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "抱歉，您似乎沒有權限存取該文件。", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "找不到頁面", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "離線", "We were unable to load the document while offline.": "我們無法在離線狀態下讀取文件。", "Your account has been suspended": "您的使用者帳號已經被停用", "Warning Sign": "警告", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "工作區管理員<em>{{ suspendedContactEmail }}</em>已暫停您的帳戶。要重新啟用您的帳戶，請直接聯繫他們。", "Created by me": "由我建立的", "Weird, this shouldn’t ever be empty": "怪了，這不應該是空的", "You haven’t created any documents yet": "您還沒有建立任何文件", "Documents you’ve recently viewed will be here for easy access": "您最近檢視過的文件會被列於此方便您快速存取", "We sent out your invites!": "我們發出了您的邀請！", "Those email addresses are already invited": "這些電子郵件地址早前已被邀請過", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "抱歉，您一次最多只能發送 {{MAX_INVITES}} 個邀請", "Invited {{roleName}} will receive access to": "受邀的 {{roleName}} 將獲得對……的訪問權限。", "{{collectionCount}} collections": "{{collectionCount}}個文件集", "Admin": "管理員", "Can manage all workspace settings": "可以管理所有工作區的設定", "Can create, edit, and delete documents": "可以建立、編輯、刪除文件", "Can view and comment": "可以查看評論", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "邀請他人加入你的工作空間。他們可以使用{{signinMethods}}或電子郵件地址登錄。", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "邀請成員加入您的工作區。他們需要使用 {{signinMethods}} 登入。", "As an admin you can also <2>enable email sign-in</2>.": "\b身為管理員，您也可以啟用<2>使用電子郵件登入</2>功能。", "Invite as": "邀請為", "Role": "角色", "Email": "電子郵件", "Add another": "新增另一個", "Inviting": "正在邀請", "Send Invites": "發送邀請", "Open command menu": "打開指令選單", "Forward": "往前", "Edit current document": "編輯目前文件", "Move current document": "移動目前文件", "Open document history": "打開文件歷史紀錄", "Jump to search": "跳轉到搜尋", "Jump to home": "跳轉到首頁", "Focus search input": "將游標移至搜尋框", "Open this guide": "打開這份指南", "Enter": "Enter", "Publish document and exit": "發佈並退出", "Save document": "儲存文件", "Cancel editing": "取消編輯", "Collaboration": "合作", "Formatting": "格式化", "Paragraph": "段落", "Large header": "大標題", "Medium header": "中標題", "Small header": "小標題", "Underline": "底線", "Undo": "復原", "Redo": "重做", "Lists": "清單", "Toggle task list item": "切換任務清單項目", "Tab": "Tab", "Indent list item": "增加清單縮排", "Outdent list item": "減少清單縮排", "Move list item up": "將清單項目往上移", "Move list item down": "將清單項目往下移", "Tables": "表格", "Insert row": "插入列", "Next cell": "下一個儲存格", "Previous cell": "前一個儲存格", "Space": "空白鍵", "Numbered list": "編號清單", "Blockquote": "引用區塊", "Horizontal divider": "水平分隔線", "LaTeX block": "LaTeX 區塊", "Inline code": "行內程式碼", "Inline LaTeX": "行內 LaTeX", "Triggers": "觸發器", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "插入區塊", "Sign In": "登入", "Continue with Email": "使用電子郵件以繼續", "Continue with {{ authProviderName }}": "使用 {{ authProviderName }} 以繼續", "Back to home": "回到首頁", "The workspace could not be found": "找不到工作區", "To continue, enter your workspace’s subdomain.": "輸入工作區的子網域以繼續", "subdomain": "子網域", "Continue": "繼續", "The domain associated with your email address has not been allowed for this workspace.": "此工作區不允許與您的電子郵件地址的網域。", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "無法登入。請瀏覽至您工作區的自訂URL，再重新嘗試登入。<1></1>如果您是被邀請至工作區，你會在邀請信件中找到連結。", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "抱歉，無法使用個人 Gmail 地址建立新帳號。<1></1>請改用 Google Workspaces 帳號。", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "與您的使用者相關聯的工作區已安排刪除，目前無法訪問。", "The workspace you authenticated with is not authorized on this installation. Try another?": "您進行身份驗證的工作區在此安裝中未獲得授權。嘗試另一個？", "We could not read the user info supplied by your identity provider.": "我們無法讀取您的身份提供商提供的使用者資訊。", "Your account uses email sign-in, please sign-in with email to continue.": "您的帳號使用電子郵件登入，請使用電子郵件登入才能繼續。", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "最近發送了電子郵件登入連結，請檢查您的收件箱或幾分鐘後重試。", "Authentication failed – we were unable to sign you in at this time. Please try again.": "身份驗證失敗 – 我們目前無法讓您登入。請再試一次。", "Authentication failed – you do not have permission to access this workspace.": "身份驗證失敗 – 您無權訪問此工作區。", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "抱歉，該登入連結似乎不再有效，請嘗試請求另一個。", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "您的帳戶已被停用。請聯繫工作區管理員重新啟用您的帳號。", "This workspace has been suspended. Please contact support to restore access.": "這個工作區已被停用。請聯繫支援以回復存取權限。", "Authentication failed – this login method was disabled by a workspace admin.": "身份驗證失敗——此登錄方法已被工作區管理員禁用。", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "您正嘗試加入的工作區需要先得到邀請才能建立帳戶。<1></1>請向工作區管理員獲得邀請後再試一次。", "Sorry, an unknown error occurred.": "抱歉，發生了未知的錯誤。", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "工作區名字", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "登入", "Error": "發生錯誤", "Failed to load configuration.": "設定載入失敗", "Check the network requests and server logs for full details of the error.": "檢查網路請求和伺服器日誌以獲取錯誤的完整詳細信息。", "Custom domain setup": "自訂域名設置", "Almost there": "快完成了", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "您的自訂域名已成功指向 Outline。要完成設置過程，請聯繫客服人員。", "Choose workspace": "選擇工作區", "This login method requires choosing your workspace to continue": "此登入方法需要選擇您的工作區才能繼續", "Check your email": "確認您的電子郵件", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "如果帳戶存在，登錄連結會被發送到以下電子郵件 <em>{{ emailLinkSentTo }}</em>。", "Back to login": "返回登入頁面", "Get started by choosing a sign-in method for your new workspace below…": "請為你新建的工作區選擇一種登入方式...", "Login to {{ authProviderName }}": "登入到 {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "您上次使用 {{ authProviderName }} 進行登入。", "Or": "或", "Already have an account? Go to <1>login</1>.": "已經有帳號了嗎？馬上 <1>登入</1>。", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "任何文件集", "All time": "全天", "Past day": "過去一天", "Past week": "過去一週", "Past month": "過去一個月", "Past year": "過去一年", "Any time": "任何時間", "Remove document filter": "移除文件篩選器", "Any status": "任何狀態", "Remove search": "刪除搜索", "Any author": "任何作者", "Search titles only": "僅搜尋標題", "Something went wrong": "發生了某些錯誤", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "沒有符合篩選條件的文件。", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API 金鑰可用於對 API 進行身份驗證，並透過程式控制您工作區的資料。如需更多詳細資訊，請參閱<em>開發人員文件</em>。", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "来自 {{ name }}", "Last used": "最近使用", "No expiry": "永久有效", "Restricted scope": "範圍受限", "API key copied to clipboard": "API 金鑰已複製到剪貼簿", "Copied": "已複製", "Are you sure you want to revoke the {{ tokenName }} token?": "您確定要撤銷 {{ tokenName }} 權杖嗎？", "Disconnect integration": "中斷整合", "Connected": "已連接", "Disconnect": "解除連結", "Disconnecting": "正在中斷", "Allowed domains": "域名白名單", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "應該要可以使用SSO建立新帳號的網域。更改這個並不會影響現有的帳號。", "Remove domain": "移除網域", "Add a domain": "添加網域", "Save changes": "保存變更", "Please choose a single file to import": "請選擇要匯入的檔案", "Your import is being processed, you can safely leave this page": "您的匯入正在處理中，您可以安全地離開此頁面", "File not supported – please upload a valid ZIP file": "該文件不被支持 - 請上傳有效的 ZIP 壓縮文件", "Set the default permission level for collections created from the import": "為從匯入建立的文件集建立預設權限", "Uploading": "正在上傳", "Start import": "開始匯入", "Processing": "正在處理", "Expired": "已逾時", "Completed": "已完成", "Failed": "失敗", "All collections": "所有的文件集", "Import deleted": "匯出已刪除", "Export deleted": "匯出已刪除", "Are you sure you want to delete this import?": "您確定要刪除該匯入檔案嗎？", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "刪除這個匯入檔案也會刪除透過這個建立的所有文件集及文件。這無法被取消。", "Check server logs for more details.": "檢查服務器日誌", "{{userName}} requested": "{{userName}} 已請求", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "透過群組功能組織您的團隊。基於職務或責任範圍進行分組的效果做好——例如支援團隊或工程團隊。", "You’ll be able to add people to the group next.": "接下來，您可以將人員新增到群組中。", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "您可以在任何時間修改群組名稱，但若您太經常修改群組名稱可能會導致團隊成員困擾。", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "您確定嗎？刪除 <em>{{groupName}}</em> 群組將會導致其成員失去與該群組關聯之文件集與文件存取權限。", "Add people to {{groupName}}": "新增人員到 {{groupName}}", "{{userName}} was removed from the group": "{{userName}} 已經從群組中移除", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "添加到<em>{{groupName}}</em>群組的成員，他們將能夠訪問被添加到該群組的任何文件集。", "Add people": "新增人員", "Listing members of the <em>{{groupName}}</em> group.": "列出 <em>{{groupName}}</em> 群組的成員。", "This group has no members.": "這個群組沒有成員。", "{{userName}} was added to the group": "{{userName}} 已經被加入到群組", "Could not add user": "無法新增使用者", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "添加下面的成員以讓他們訪問該群組。需要添加還不是成員的用戶嗎？", "Invite them to {{teamName}}": "邀請他們加入 {{teamName}}", "Ask an admin to invite them first": "請管理員先邀請他們。", "Search by name": "依名稱搜尋", "Search people": "搜尋使用者", "No people matching your search": "找不到符合搜尋條件的使用者", "No people left to add": "所有人員都已經被新增", "Date created": "創建日期", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "這是如何運作的？", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "你可以輸入自先前其他實例透過JSON選項輸出的zip檔案。在{{ appName }}中，在側邊欄的設定中打開<em>匯出</em>並且選取<em>匯出資料</em>。", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "請將從 {{appName}} 的 JSON 匯出選項中匯出的 zip 檔案拖放到此處，或點擊上傳", "Canceled": "已取消", "Import canceled": "匯入已取消", "Are you sure you want to cancel this import?": "您確定要取消匯入嗎？", "Canceling": "正在取消", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "你可以輸入自先前其他Outline實例輸出的zip檔案 – 文件集，文件，以及照片都會被匯入。在Outline中，在側邊欄的設定中打開<em>匯出</em>並且選取<em>匯出資料</em>。", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "請將從 {{appName}} 的 Markdown 匯出選項中匯出的 zip 檔案拖放到此處，或點擊上傳", "Configure": "Configure", "Connect": "連結", "Last active": "最近一次活動", "Guest": "訪客", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "分享自", "Date shared": "分享日期", "Last accessed": "最近存取", "Domain": "網域", "Views": "瀏覽次數", "All roles": "所有角色", "Admins": "管理員", "Editors": "編輯者", "All status": "所有狀態", "Active": "上線", "Left": "左", "Right": "右", "Settings saved": "設定已儲存", "Logo updated": "標誌已更新", "Unable to upload new logo": "無法上傳新 Logo", "Delete workspace": "刪除工作區", "These settings affect the way that your workspace appears to everyone on the team.": "這些設定會影響所有團隊成員所看到的工作區外觀。", "Display": "介面", "The logo is displayed at the top left of the application.": "標誌圖案會被顯示在應用程式的左上方。", "The workspace name, usually the same as your company name.": "工作區的名稱，通常與您的公司名稱相同。", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "外觀主題", "Customize the interface look and feel.": "自定義界面的外觀和感覺。", "Reset theme": "重置主題", "Accent color": "強調色", "Accent text color": "強調文字顏色", "Public branding": "品牌形象", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "目錄位置", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "行為", "Subdomain": "子網域", "Your workspace will be accessible at": "您的工作區可以透過此超連結存取", "Choose a subdomain to enable a login page just for your team.": "選擇一個子域名來為您的團隊啟用登入頁面。", "This is the screen that workspace members will first see when they sign in.": "這是工作區成員登入時首先會看到的介面。", "Danger": "危險", "You can delete this entire workspace including collections, documents, and users.": "您可以刪除整個工作區，包括文件集，文件，以及使用者。", "Export data": "匯出數據", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "匯出所有文件集將會需要一些時間，可以的話，請考慮匯出單一的文件或文件集。我們會將您擁有的所有文件轉換為 Markdown 格式，並封裝為一個 zip 壓縮檔。\n在匯出作業開始後您即可離開這個頁面 – 如果您有開啟通知，匯出完成時我們會透過電子郵件寄送連結到 <em>{{ userEmail }}</em>。", "Recent exports": "最近匯出的", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "管理可選和 Beta 測試的功能，更改這些設定會影響所有工作區的成員。", "Separate editing": "分開編輯", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "啟用時，文件預設採用單獨的編輯模式，而非始終處於可編輯狀態。此設定可被使用者偏好所覆蓋。", "When enabled team members can add comments to documents.": "啓用時，團隊成員可以在文件中新增留言。", "Create a group": "建立群組", "Could not load groups": "無法移除群組", "New group": "建立群組", "Groups can be used to organize and manage the people on your team.": "群組可以用來組織與管理團隊中的人員。", "No groups have been created yet": "尚未建立任何群組", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "匯入包含 Markdown 文檔的 zip 壓縮檔案 (需要從 0.67.0 或更早版本匯出)", "Import data": "匯入數據", "Import a JSON data file exported from another {{ appName }} instance": "匯入從另一個 {{ appName }} 實例匯出的 JSON 資料檔案", "Import pages from a Confluence instance": "從 Confluence 實例匯入文件", "Enterprise": "企業用戶", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "快速將您現有的文件、頁面和檔案從其他工具和服務轉移到 {{appName}}。您還可以直接將任何 HTML、Markdown 和純文本文件拖放到應用程式中的文件集中。", "Recent imports": "最近匯入的", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "無法移除群組", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "", "Receive a notification whenever a new document is published": "當新文件被發布時接收通知", "Document updated": "文件已更新", "Receive a notification when a document you are subscribed to is edited": "當您訂閱的文件被編輯時收到通知", "Comment posted": "評論已發佈", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "當您訂閱的文件或參與的話題收到評論時收到通知", "Mentioned": "提及到", "Receive a notification when someone mentions you in a document or comment": "當有人在文件或評論中提及您時收到通知", "Receive a notification when a comment thread you were involved in is resolved": "Receive a notification when a comment thread you were involved in is resolved", "Collection created": "文件集已建立", "Receive a notification whenever a new collection is created": "當新的文件集建立時接收通知", "Invite accepted": "邀請已被接受", "Receive a notification when someone you invited creates an account": "當您邀請的人建立帳戶時收到通知", "Invited to document": "邀請到文件", "Receive a notification when a document is shared with you": "有文件分享給你時收到通知", "Invited to collection": "邀請到文件集", "Receive a notification when you are given access to a collection": "被授予文件集權限時收到通知", "Export completed": "匯出已完成", "Receive a notification when an export you requested has been completed": "當您要求的匯出完成時收到通知", "Getting started": "快速入門", "Tips on getting started with features and functionality": "幫助您開始使用 Outline 功能的提示", "New features": "新功能", "Receive an email when new features of note are added": "當有新功能增加到筆記時接收電子郵件", "Notifications saved": "已保存通知", "Unsubscription successful. Your notification settings were updated": "取消訂閱成功。您的通知設定已被更新", "Manage when and where you receive email notifications.": "管理您接收電子郵件通知的時間和地點。", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "電子郵件整合目前已停用，若要啟用通知，請設定相關的環境變數並重新啟動伺服器", "Preferences saved": "已保存偏好設定", "Delete account": "刪除帳號", "Manage settings that affect your personal experience.": "管理影響您個人體驗的設定。", "Language": "語言", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "選擇介面語言。您可透過我們的 <2> 翻譯平台傳送門 </2> 為社群提供協作翻譯。", "Choose your preferred interface color scheme.": "選擇偏好的介面配色方案。", "Use pointer cursor": "使用指標", "Show a hand cursor when hovering over interactive elements.": "將滑鼠停留在互動元素上時顯示手形游標。", "Show line numbers": "顯示行數", "Show line numbers on code blocks in documents.": "在文件裏的代碼塊中顯示行數。", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "啟用時，文件進入獨立的編輯模式。停用時，只要您有足夠權限，始終可對文件進行編輯。", "Remember previous location": "記住上一次的位置", "Automatically return to the document you were last viewing when the app is re-opened.": "在打開應用程式時自動開啓上一次檢視的文件。", "Smart text replacements": "智慧文字取代", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "自動編排文字格式，將捷徑鍵取代為符號、破折號、智慧引號及其他印刷樣式元素。", "You may delete your account at any time, note that this is unrecoverable": "您隨時可以刪除您的使用者帳號，但此為不可回復之動作", "Profile saved": "個人資料已儲存", "Profile picture updated": "大頭貼已上傳", "Unable to upload new profile picture": "無法上傳新的大頭貼", "Manage how you appear to other members of the workspace.": "管理您在工作區中的樣子。", "Photo": "照片", "Choose a photo or image to represent yourself.": "選擇一張照片或圖片來代表您。", "This could be your real name, or a nickname — however you’d like people to refer to you.": "這可以是您的真實姓名或暱稱，或任何您希望別人用來稱呼您的名字。", "Email address": "電子郵件地址", "Are you sure you want to require invites?": "您確定您想要獲得邀請嗎？", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "新使用者在創建賬號前需要先被邀請。 <em>預設身份</em> 和 <em>允許使用的域名</em> 都將不再適用。", "Settings that impact the access, security, and content of your workspace.": "此處設定將影響您的知識庫存取、安全性、內容。", "Allow members to sign-in with {{ authProvider }}": "允許成員使用 {{ authProvider }} 登入", "Disabled": "已停用", "Allow members to sign-in using their email address": "允許成員使用他們的電子郵件地址登入", "The server must have SMTP configured to enable this setting": "伺服器必須有已組態的 SMTP 才能啟用此設定", "Access": "存取", "Allow users to send invites": "允許使用者發送邀請", "Allow editors to invite other people to the workspace": "允許編輯者邀請其他人到工作區", "Require invites": "需要被邀請", "Require members to be invited to the workspace before they can create an account using SSO.": "成員必須先受邀加入工作區，才能使用 SSO 建立帳號。", "Default role": "預設角色", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "預設角色設定只會套用到新的使用者帳號。改變此設定並不會影響現有使用者帳號。", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "啟用後，工作區中的任何成員都可以在網際網絡上公開共享工作區中的文件", "Viewer document exports": "檢視者可匯出文件", "When enabled, viewers can see download options for documents": "啓用時，檢視者可以看到文件的下載選項", "Users can delete account": "使用者可以刪除帳號", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "顯示嵌入的外部服務", "Links to supported services are shown as rich embeds within your documents": "支援的外部服務連結將會被嵌入顯示於您的文件中", "Collection creation": "文件集已建立", "Allow editors to create new collections within the workspace": "允許編輯者在工作區內建立新的文件集", "Workspace creation": "建立工作區", "Allow editors to create new workspaces": "允許編輯者建立新的工作區", "Could not load shares": "無法移除群組", "Sharing is currently disabled.": "已停用分享功能。", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "您可以透過<em>安全性設定</em>針對全域啟用或停用文件公開分享的功能。", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "下列清單為已被分享之文件。任何擁有公開分享連結的人，在分享連結被註銷前，都可以存取文件的唯讀版本。", "You can create templates to help your team create consistent and accurate documentation.": "您可以建立文件範本來幫助團隊建立一致且準確的文件。", "Alphabetical": "依照字母", "There are no templates just yet.": "目前還沒有任何文件範本。", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "確認碼已發送至您的電子郵件，請輸入確認碼以永久地刪除這個工作區。", "Confirmation code": "驗證碼", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "刪除<1>{{workspaceName}}</1>工作區會刪除所有文件集，文件，使用者，以及相關資料。您會立即從{{appName}} 登出。", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "請留意工作區是完全分開的。它們可以有不同的網域，設定，使用者，帳務資訊。", "You are creating a new workspace using your current account — <em>{{email}}</em>": "您正在使用當前帳戶建立新的工作區 — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "要使用另一個電子郵件建立工作區請從首頁登入", "Trash emptied": "垃圾桶已清空", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "您確定要永久刪除文件 {} 嗎？這個動作會立即生效且無法被回復。", "Recently deleted": "最近刪除", "Trash is empty at the moment.": "垃圾桶目前是空的。", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "確認碼已發送至您的電子郵件，請輸入確認碼以永久地刪除您的帳號。", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "您確定嗎？刪除您的帳號將會破壞您的使用者個人資料而且將無法還原。您將會被立刻從 {{appName}} 被登出，同時所有的 API 權杖都會被撤銷。", "Delete my account": "刪除我的帳號", "Today": "今日", "Yesterday": "昨日", "Last week": "上星期", "This month": "這個月", "Last month": "上個月", "This year": "今年", "Expired yesterday": "昨天已到期", "Expired {{ date }}": "{{ date }} 已到期", "Expires today": "今天到期", "Expires tomorrow": "明天到期", "Expires {{ date }}": "{{ date }} 到期", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "喔歐！您必須在 GitHub 允許 {{appName}} 取得權限連結您的工作區。再試一次？", "Something went wrong while authenticating your request. Please try logging in again.": "請求認證時發生一些錯誤，請嘗試重新登入。", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "透過將 GitHub 組織或特定儲存庫連接到 {appName}，啟用 GitHub issues的預覽並在文件中拉取請求。", "Enabled by {{integrationCreatedBy}}": "由 {{integrationCreatedBy}} 啟用", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "Slack 整合目前已停用，若要啟用，請設定相關的環境變數並重新啟動伺服器。", "Google Analytics": "Google 分析", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "添加你的 Google Analytics ，以將知識庫的文件檢視和分析數據發送到您自己的 Google Analytics 帳戶。", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "在 Google Analytics 管理儀表板中創建 \"Web\" 流，並從生成的程式碼片段中複製 measurement ID 進行安裝。", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "執行個體 URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "網頁 ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "從 Notion 匯入文件", "Add to Slack": "新增到 Slack", "document published": "已發佈文件", "document updated": "已更新文件", "Posting to the <em>{{ channelName }}</em> channel on": "連結到 <em>{{ channelName }}</em> 頻道", "These events should be posted to Slack": "這些活動應該被發佈到 Slack", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "喔歐！您必須在 Slack 允許 {{appName}} 取得權限連結您的工作區。再試一次？", "Personal account": "個人帳號", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "取消連結您的個人帳號將不會從 Slack 搜尋文件，您確定嗎", "Slash command": "斜線指令", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "使用 <em>{{ command }}</em> slash command 功能搜尋文件不需跳開聊天視窗，而且可以在 Slack 獲得更多細節的 {{ appName }} 文件預覽功能。", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "將 {{appName}} 文件集連結到 Slack 頻道。若文件已經被更新或公開釋出，則會被自動張貼在 Slack 上。", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "如何使用 {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "要搜尋您的工作區請使用 {{ command }}.\n輸入 {{ command2 }} 來顯示這個幫助文字。", "Post to Channel": "發送到頻道", "This is what we found for \"{{ term }}\"": "這是我們找到關於 \"{{ term }}\" 的搜尋結果", "No results for \"{{ term }}\"": "找不到任何 ”{{ term }}“ 的搜尋結果", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "連結到你的帳號", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "指令碼名稱", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "您確定要刪除 {{ name }} webhook 嗎？", "Webhook updated": "已更新 Webhook", "Update": "更新", "Updating": "正在更新", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "提供一個描述名稱給這個Webhook以及當符合特定事件時我們要傳送POST要求的URL。", "A memorable identifer": "容易記憶的辨識名稱", "URL": "URL", "Signing secret": "簽署密鑰", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "訂閱所有事件、群組或個人事件。我們建議僅訂閱應用程序運行所需的最少量事件。", "All events": "所有活動", "All {{ groupName }} events": "所有 {{ groupName }} 活動", "Delete webhook": "刪除 Webhook", "Subscribed events": "訂閱的活動", "Edit webhook": "編輯 Webhook", "Webhook created": "已建立 Webhook", "Webhooks": "Webhooks", "New webhook": "新 Webhook", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhooks 用在當 {{appName}} 事件發生時通知您的應用程式。事件會使用帶有 JSON payload 的 https 請求近乎即時地發送。", "Inactive": "已停用", "Create a webhook": "建立 Webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier是一個可以讓 {{appName}} 簡單的跟上千個商業工具整合的平台。自動化你的工作流程，同步資料，以及更多。", "Never logged in": "從未登入", "Online now": "正在線上", "Online {{ timeAgo }}": "{{ timeAgo }} 前上線", "Viewed just now": "剛剛查看過", "You updated {{ timeAgo }}": "{{ timeAgo }} 由您更新", "{{ user }} updated {{ timeAgo }}": "{{ timeAgo }} 由 {{ user }} 更新", "You created {{ timeAgo }}": "{{ timeAgo }} 由您新增", "{{ user }} created {{ timeAgo }}": "{{ timeAgo }} 由 {{ user }} 新增", "Error loading data": "Error loading data"}