{"New API key": "新建 API 密钥", "Open collection": "打开文档集", "New collection": "新建文档集", "Create a collection": "创建文档集", "Edit": "编辑", "Edit collection": "编辑文档集", "Permissions": "权限", "Collection permissions": "文档集权限", "Share this collection": "分享此文档集", "Search in collection": "在文档集中搜索", "Star": "收藏", "Unstar": "取消收藏", "Subscribe": "订阅", "Subscribed to document notifications": "订阅文档通知", "Unsubscribe": "取消订阅", "Unsubscribed from document notifications": "取消订阅文档通知", "Archive": "归档", "Archive collection": "归档文档集", "Collection archived": "文档集已归档", "Archiving": "正在归档", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "归档此文档集也将归档其中的所有文档。该文档集中的文档将不再在搜索结果中可见。", "Restore": "恢复", "Collection restored": "文档集已恢复", "Delete": "删除", "Delete collection": "删除文档集", "New template": "新建模板", "Delete comment": "删除评论", "Mark as resolved": "标记为已解决", "Thread resolved": "话题已解决", "Mark as unresolved": "标记为未解决", "View reactions": "", "Reactions": "回应", "Copy ID": "复制 ID", "Clear IndexedDB cache": "清除 IndexedDB 缓存", "IndexedDB cache cleared": "IndexedDB 缓存已清除", "Toggle debug logging": "切换调试日志", "Debug logging enabled": "调试日志已启用", "Debug logging disabled": "调试日志已禁用", "Development": "开发", "Open document": "打开文档", "New document": "新建文档", "New draft": "新建草稿", "New from template": "从模板新建", "New nested document": "新建子文档", "Publish": "发布", "Published {{ documentName }}": "已发布 {{ documentName }}", "Publish document": "发布文档", "Unpublish": "取消发布", "Unpublished {{ documentName }}": "取消发布 {{ documentName }}", "Share this document": "共享此文档", "HTML": "HTML", "PDF": "PDF", "Exporting": "正在导出", "Markdown": "<PERSON><PERSON>", "Download": "下载", "Download document": "下载文档", "Copy as Markdown": "以 Markdown 格式复制", "Markdown copied to clipboard": "Markdown 已复制到剪贴板", "Copy as text": "复制为文本", "Text copied to clipboard": "复制文本到剪贴板", "Copy public link": "复制公开链接", "Link copied to clipboard": "链接已复制到剪贴板", "Copy link": "复制链接", "Copy": "复制", "Duplicate": "复制", "Duplicate document": "复制文档", "Copy document": "复制文档", "collection": "文档集", "Pin to {{collectionName}}": "固定到 {{collectionName}}", "Pinned to collection": "置顶到文档集", "Pin to home": "固定到主页", "Pinned to home": "已固定到主页", "Pin": "固定", "Search in document": "在文档中搜索", "Print": "打印", "Print document": "打印文档", "Import document": "导入文档", "Templatize": "模板化", "Create template": "创建模板", "Open random document": "打开随机文档", "Search documents for \"{{searchQuery}}\"": "搜索匹配 \"{{searchQuery}}\" 的文档", "Move to workspace": "移动到工作区", "Move": "移动", "Move to collection": "移动到文档集", "Move {{ documentType }}": "移动 {{ documentType }}", "Are you sure you want to archive this document?": "你确定要归档此文档吗?", "Document archived": "文件已归档", "Archiving this document will remove it from the collection and search results.": "归档此文档将从文档集和搜索结果中删除它。", "Delete {{ documentName }}": "删除{{ documentName }}", "Permanently delete": "永久删除", "Permanently delete {{ documentName }}": "永久删除{{ documentName }}", "Empty trash": "清空回收站", "Permanently delete documents in trash": "永久删除回收站中的文档", "Comments": "评论", "History": "历史记录", "Insights": "统计", "Disable viewer insights": "禁用浏览者统计", "Enable viewer insights": "启用浏览者统计", "Leave document": "离开文档", "You have left the shared document": "你已经离开了此共享文档", "Could not leave document": "无法离开文档", "Home": "主页", "Drafts": "草稿箱", "Search": "搜索", "Trash": "回收站", "Settings": "设置", "Profile": "基本资料", "Templates": "文档模板", "Notifications": "通知", "Preferences": "偏好设置", "Documentation": "帮助文档", "API documentation": "API 文档", "Toggle sidebar": "切换侧边栏", "Send us feedback": "发送反馈", "Report a bug": "报告错误", "Changelog": "更新日志", "Keyboard shortcuts": "快捷键", "Download {{ platform }} app": "下载 {{ platform }} 应用程序", "Log out": "退出登录", "Mark notifications as read": "将通知标记为已读", "Archive all notifications": "将所有通知存档", "New App": "新建应用", "New Application": "新应用", "This version of the document was deleted": "此版本的文档已被删除", "Link copied": "链接已复制", "Dark": "深色模式", "Light": "浅色模式", "System": "跟随系统", "Appearance": "界面风格", "Change theme": "更改主题", "Change theme to": "更改主题为", "Switch workspace": "切换工作区", "Select a workspace": "选择工作区", "New workspace": "新建工作区", "Create a workspace": "创建工作区", "Login to workspace": "登陆到工作区", "Invite people": "邀请其他人", "Invite to workspace": "邀请到工作区", "Promote to {{ role }}": "提升为 {{ role }}", "Demote to {{ role }}": "降级为 {{ role }}", "Update role": "更新角色", "Delete user": "删除用户", "Collection": "文档集", "Collections": "文档集", "Debug": "调试", "Document": "文档", "Documents": "文档", "Recently viewed": "最近浏览过", "Revision": "修订", "Navigation": "导航", "Notification": "通知", "People": "用户", "Workspace": "工作区", "Recent searches": "最近的搜索", "currently editing": "正在编辑", "currently viewing": "正在浏览", "previously edited": "先前已编辑", "You": "你", "Viewers": "浏览者", "Collections are used to group documents and choose permissions": "文档集用于对文档进行分组和权限管理", "Name": "名称", "The default access for workspace members, you can share with more users or groups later.": "工作区成员的默认访问权限，你可以稍后与更多的用户或组分享。", "Public document sharing": "公开共享文档", "Allow documents within this collection to be shared publicly on the internet.": "允许此文档集中的文档在互联网上公开共享。", "Commenting": "评论", "Allow commenting on documents within this collection.": "允许评论此集合中的文档。", "Saving": "保存中", "Save": "保存", "Creating": "创建中", "Create": "创建", "Collection deleted": "文档集已删除", "I’m sure – Delete": "确认删除", "Deleting": "正在删除", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "你确定吗？ 删除 <em>{{collectionName}}</em> 文档集是永久性的且无法恢复，但其中所有已发布的文档将被移至回收站。", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "另外，<em>{{collectionName}}</em> 被用作起始视图 - 删除它将重置起始视图为主页。", "Type a command or search": "输入命令或搜索", "Choose a template": "选择模板", "Are you sure you want to permanently delete this entire comment thread?": "你确定要永久删除此评论吗？", "Are you sure you want to permanently delete this comment?": "你确定要永久删除此评论吗？", "Confirm": "确认", "manage access": "管理访问权限", "view and edit access": "可编辑", "view only access": "仅浏览权限", "no access": "无访问权限", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "你无权将 {{ documentName }} 移动到文档集 {{ collectionName }} 中", "Move document": "移动文档", "Moving": "移动", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "将文档 <em>{{ title }}</em> 移动到 {{ newCollectionName }} 集合将使所有工作区成员的权限从 <em>{{ prevPermission }}</em> 更改为 <em>{{ newPermission }}</em>。", "Submenu": "子菜单", "Collections could not be loaded, please reload the app": "无法加载文档集，请重新加载应用", "Default collection": "默认文档集", "Start view": "开始浏览", "Install now": "立即安装", "Deleted Collection": "删除文档集", "Untitled": "无标题", "Unpin": "取消置顶", "{{ minutes }}m read": "阅读了{{ minutes }}分钟", "Select a location to copy": "选择要复制的目标位置", "Document copied": "文档已复制", "Couldn’t copy the document, try again?": "无法复制文档，请重试", "Include nested documents": "包含子文档", "Copy to <em>{{ location }}</em>": "复制到 <em>{{ location }}</em>", "Search collections & documents": "搜索文档集和文档", "No results found": "未找到结果", "New": "新", "Only visible to you": "仅自己可见", "Draft": "草稿", "Template": "模板", "You updated": "你已更新", "{{ userName }} updated": "{{ userName }} 已更新", "You deleted": "你已删除", "{{ userName }} deleted": "{{ userName }} 已删除", "You archived": "你归档了", "{{ userName }} archived": "{{ userName }} 已归档", "Imported": "已导入", "You created": "你已创建", "{{ userName }} created": "{{ userName }} 已创建", "You published": "你发布了", "{{ userName }} published": "{{ userName }} 已发布", "Never viewed": "从未被浏览过", "Viewed": "已浏览", "in": "在", "nested document": "子文档", "nested document_plural": "内嵌子文档", "{{ total }} task": "共 {{ total }} 项任务", "{{ total }} task_plural": "共 {{ total }} 项任务", "{{ completed }} task done": "已完成 {{ completed }} 项任务", "{{ completed }} task done_plural": "已完成 {{ completed }} 项任务", "{{ completed }} of {{ total }} tasks": "一共 {{ total }} 项任务，已完成 {{ completed }} 项", "Currently editing": "正在编辑", "Currently viewing": "正在浏览", "Viewed {{ timeAgo }}": "{{ timeAgo }} 浏览", "Module failed to load": "模块加载失败", "Loading Failed": "加载失败", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "对不起，部分应用程序加载失败。这可能是因为它在您打开选项卡后更新了，或者是因为网络请求失败。请尝试重新加载。", "Reload": "重新加载", "Something Unexpected Happened": "发生了一些意料之外的错误", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "抱歉，出现了一个无法恢复的错误{{notified}}。推测只是暂时异常，请尝试重新加载页面。", "our engineers have been notified": "已通知我们的工程师", "Show detail": "显示详情", "Revision deleted": "修订已被删除", "Current version": "当前版本", "{{userName}} edited": "{{userName}} 已编辑", "{{userName}} archived": "已被 {{userName}} 归档", "{{userName}} restored": "已被 {{userName}} 恢复", "{{userName}} deleted": "已被 {{userName}} 删除", "{{userName}} added {{addedUserName}}": "{{userName}} 添加了 {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} 移除了 {{removedUserName}}", "{{userName}} moved from trash": "已被 {{userName}} 已从回收站移出", "{{userName}} published": "已被 {{userName}} 发布", "{{userName}} unpublished": "{{userName}} 未发布", "{{userName}} moved": "已被 {{userName}} 移动", "Export started": "导出已开始", "Your file will be available in {{ location }} soon": "你的文件将很快在 {{ location }} 中可用", "View": "浏览", "A ZIP file containing the images, and documents in the Markdown format.": "包含图片和 Markdown 格式文档的 ZIP 文件。", "A ZIP file containing the images, and documents as HTML files.": "包含图片和 HTML 格式文档的 ZIP 文件。", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "结构化数据可以用于传输数据到另一个兼容的 {{ appName }} 实例。", "Export": "导出", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "导出文档集 <em>{{collectionName}}</em> 可能需要一些时间。", "You will receive an email when it's complete.": "完成后，你将收到一封电子邮件。", "Include attachments": "包含附件", "Including uploaded images and files in the exported data": "导出数据中包含上传的图片和文件", "{{count}} more user": "{{count}} 个用户", "{{count}} more user_plural": "{{count}} 个用户", "Filter": "筛选", "No results": "没有结果", "{{authorName}} created <3></3>": "{{authorName }} 已创建>", "{{authorName}} opened <3></3>": "{{authorName}} 已打开 <3></3>", "Search emoji": "搜索 Emoji", "Search icons": "搜索图标", "Choose default skin tone": "选择默认皮肤色调", "Show menu": "显示菜单", "Icon Picker": "图标选择器", "Icons": "图标", "Emojis": "表情包", "Remove": "移除", "All": "全部", "Frequently Used": "经常使用的", "Search Results": "搜索结果", "Smileys & People": "表情 & 人物", "Animals & Nature": "动物 & 自然", "Food & Drink": "食物 & 饮料", "Activity": "活动", "Travel & Places": "旅行 & 地点", "Objects": "物品", "Symbols": "符号", "Flags": "标志", "Select a color": "选择颜色", "Loading": "加载中", "Permission": "权限", "View only": "仅浏览", "Can edit": "可编辑", "No access": "无访问权限", "Default access": "默认访问权限", "Change Language": "更改语言", "Dismiss": "忽略", "You’re offline.": "你已离线。", "Sorry, an error occurred.": "抱歉，出错了。", "Click to retry": "点击重试", "Back": "返回", "Unknown": "未知", "Mark all as read": "全部标记为已读", "You're all caught up": "你已了解所有情况", "Icon": "图标", "My App": "我的应用", "Tagline": "标识语", "A short description": "一个简短的描述", "Callback URLs": "回调 URL", "Published": "已发布", "Allow this app to be installed by other workspaces": "允许其他工作区安装此应用", "{{ username }} reacted with {{ emoji }}": "{{ username }} 回应 {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} 和 {{ secondUsername }} 已回应 {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} 和 {{ count }} 其他人已回应 {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} 和 {{ count }} 其他人已回应 {{ emoji }}", "Add reaction": "添加回应", "Reaction picker": "回应选择器", "Could not load reactions": "无法加载回应", "Reaction": "回应", "Results": "结果", "No results for {{query}}": "没有关于 {{query}} 的结果", "Manage": "管理", "All members": "所有成员", "Everyone in the workspace": "工作区中的所有人", "{{ count }} member": "{{ count }} 位成员", "{{ count }} member_plural": "{{ count }} 位成员", "Invite": "邀请", "{{ userName }} was added to the collection": "{{ userName }} 已添加到文档集", "{{ count }} people added to the collection": "已添加 {{ count }} 人到文档集中", "{{ count }} people added to the collection_plural": "已添加 {{ count }} 人到文档集中", "{{ count }} people and {{ count2 }} groups added to the collection": "已添加 {{ count }} 人和 {{ count2 }} 组到文档集中", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "已添加 {{ count }} 人和 {{ count2 }} 组到文档集中", "Add": "添加", "Add or invite": "添加或邀请", "Viewer": "浏览者", "Editor": "编辑者", "Suggestions for invitation": "对邀请的建议", "No matches": "无匹配项", "Can view": "可浏览", "Everyone in the collection": "文档集中的所有人", "You have full access": "你有完全访问权限", "Created the document": "已创建文档", "Other people": "其他人", "Other workspace members may have access": "其他工作区成员也可以访问", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "此文档可能通过你没有访问权限的父级文档或文档集与更多工作区成员共享。", "Access inherited from collection": "", "{{ userName }} was removed from the document": "已从文档中移除 {{ userName }}", "Could not remove user": "无法删除用户", "Permissions for {{ userName }} updated": "{{ userName }} 的权限已更新", "Could not update user": "无法更新用户", "Has access through <2>parent</2>": "通过 <2>父级</2> 访问", "Suspended": "已停用", "Invited": "已邀请", "Active <1></1> ago": " <1></1> 前活跃", "Never signed in": "从未登录", "Leave": "离开", "Only lowercase letters, digits and dashes allowed": "只允许使用小写字母、数字和破折号", "Sorry, this link has already been used": "抱歉，该链接已被占用", "Public link copied to clipboard": "公开链接已复制到剪贴板", "Web": "网页", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "知道该链接的任何人都可以访问，因为父级文档 <2>{{documentTitle}}</2> 已共享", "Allow anyone with the link to access": "允许所有知道链接的人访问", "Publish to internet": "发布到互联网", "Search engine indexing": "搜索引擎索引", "Disable this setting to discourage search engines from indexing the page": "禁用此设置以阻止搜索引擎对该页建立索引", "Show last modified": "显示最后修改", "Display the last modified timestamp on the shared page": "在分享页面上显示最后修改的时间戳", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "嵌套文档不会在网页上共享。切换共享以启用访问，这将是未来的默认行为。", "{{ userName }} was added to the document": "{{ userName }} 已被添加到此文档", "{{ count }} people added to the document": "{{ count }} 个人被添加到此文档", "{{ count }} people added to the document_plural": "{{ count }} 个人被添加到此文档", "{{ count }} groups added to the document": "{{ count }} 个组被添加到此文档", "{{ count }} groups added to the document_plural": "{{ count }} 个组被添加到此文档", "Logo": "网站图标", "Archived collections": "归档文档集", "New doc": "新建文档", "Empty": "空", "Collapse": "收起", "Expand": "展开", "Document not supported – try Markdown, Plain text, HTML, or Word": "不支持的文档类型 - 请用Markdown，纯文本，HTML或Word格式", "Go back": "返回", "Go forward": "前进", "Could not load shared documents": "无法加载共享文档", "Shared with me": "与我共享", "Show more": "显示更多", "Could not load starred documents": "无法加载星标文件", "Starred": "已加星标", "Up to date": "已是最新版本", "{{ releasesBehind }} versions behind": "落后 {{ <PERSON><PERSON>eh<PERSON> }} 个版本", "{{ releasesBehind }} versions behind_plural": "落后 {{ <PERSON><PERSON>eh<PERSON> }} 个版本", "Change permissions?": "更改权限？", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} 不能在 {{ parentDocumentName }} 中被移动", "You can't reorder documents in an alphabetically sorted collection": "你不能在按字母排序的文档集中排序", "The {{ documentName }} cannot be moved here": "{{ documentName }} 不能被移动到这里", "Return to App": "返回应用", "Installation": "安装", "Unstar document": "取消收藏文档", "Star document": "收藏文档", "Template created, go ahead and customize it": "模板已创建，继续进行自定义", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "从 <em>{{titleWithDefault}}</em> 创建模板是一个非破坏性的操作 - 我们将制作一个文档副本，并将其变成一个可以用作新文档起点的模板。", "Enable other members to use the template immediately": "允许其他成员立即使用该模板", "Location": "地点", "Admins can manage the workspace and access billing.": "管理员可以管理工作区和访问账单。", "Editors can create, edit, and comment on documents.": "编辑者可以创建、编辑和评论文档。", "Viewers can only view and comment on documents.": "浏览者只能浏览和评论文档。", "Are you sure you want to make {{ userName }} a {{ role }}?": "你确定要设置 {{ userName }} 作为 {{ role }} 吗？", "I understand, delete": "我了解，删除", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "你确定要永久删除 {{ userName }} 吗？此操作不可恢复，请考虑停用用户而非删除。", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "你确定要停用账户 {{ userName }} 吗？被停用的用户将无法登录。", "New name": "新的名称", "Name can't be empty": "名称不能为空", "Check your email to verify the new address.": "请检查您的电子邮件以验证新地址。", "The email will be changed once verified.": "验证后电子邮件将被更改。", "You will receive an email to verify your new address. It must be unique in the workspace.": "您将收到一封电子邮件来验证您的新地址。它在工作区中是唯一的。", "A confirmation email will be sent to the new address before it is changed.": "确认邮件将在更改前发送到新地址。", "New email": "新的邮箱地址", "Email can't be empty": "邮箱地址不能为空", "Your import completed": "导入已完成", "Previous match": "上一匹配", "Next match": "下一匹配", "Find and replace": "查找并替换", "Find": "查找", "Match case": "匹配大小写", "Enable regex": "启用正则表达式", "Replace options": "替换选项", "Replacement": "替换", "Replace": "替换", "Replace all": "全部替换", "Profile picture": "个人头像", "Create a new doc": "新建文档", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} 不会被通知，因为他们没有访问此文档的权限", "Keep as link": "保留为链接", "Mention": "提及", "Embed": "嵌入", "Add column after": "在后面添加列", "Add column before": "在前面添加列", "Add row after": "在后面添加行", "Add row before": "在前面添加行", "Align center": "居中对齐", "Align left": "左对齐", "Align right": "右对齐", "Default width": "默认宽度", "Full width": "全屏宽度", "Bulleted list": "无序列表", "Todo list": "任务列表", "Code block": "代码块", "Copied to clipboard": "已复制到剪切板", "Code": "代码", "Comment": "评论", "Create link": "创建链接", "Sorry, an error occurred creating the link": "抱歉，创建链接时发生错误", "Create a new child doc": "创建一个新的子文档", "Delete table": "删除表格", "Delete file": "删除文件", "Width x Height": "宽度 x 高度", "Download file": "下载文件", "Replace file": "替换文件", "Delete image": "删除图片", "Download image": "下载图片", "Replace image": "替换图片", "Italic": "斜体", "Sorry, that link won’t work for this embed type": "抱歉，该链接不适用于此嵌入类型", "File attachment": "附件", "Enter a link": "输入一个链接", "Big heading": "主标题", "Medium heading": "次标题", "Small heading": "小标题", "Extra small heading": "附加小标题", "Heading": "标题", "Divider": "分割线", "Image": "图片", "Sorry, an error occurred uploading the file": "抱歉，上传文件时发生错误", "Write a caption": "撰写一个标题", "Info": "信息", "Info notice": "提示信息", "Link": "链接", "Highlight": "高亮", "Type '/' to insert": "输入'/'来插入", "Keep typing to filter": "继续输入以过滤", "Open link": "打开链接", "Go to link": "转到链接", "Sorry, that type of link is not supported": "抱歉，尚不支持该类型的链接", "Ordered list": "有序列表", "Page break": "分页符", "Paste a link": "粘贴链接", "Paste a {{service}} link…": "粘贴一个 {{service}} 链接…", "Placeholder": "占位符", "Quote": "引用", "Remove link": "移除链接", "Search or paste a link": "搜索或粘贴一个链接", "Strikethrough": "删除线", "Bold": "加粗", "Subheading": "副标题", "Sort ascending": "升序排序", "Sort descending": "降序排序", "Table": "表格", "Export as CSV": "导出为 CSV", "Toggle header": "切换标题", "Math inline (LaTeX)": "行内数学 (LaTeX)", "Math block (LaTeX)": "数学块 (LaTeX)", "Merge cells": "合并单元格", "Split cell": "拆分单元格", "Tip": "提示", "Tip notice": "提示信息", "Warning": "警告", "Warning notice": "警告信息", "Success": "成功", "Success notice": "成功通知", "Current date": "当前日期", "Current time": "当前时间", "Current date and time": "当前日期和时间", "Indent": "缩进", "Outdent": "减少缩进", "Video": "视频", "None": "空", "Could not import file": "无法导入文件", "Unsubscribed from document": "取消订阅文档", "Unsubscribed from collection": "取消订阅集合", "Account": "账号", "API & Apps": "API 与应用程序", "Details": "详情", "Security": "安全性", "Features": "功能", "Members": "成员", "Groups": "群组", "API Keys": "API 密钥", "Applications": "应用", "Shared Links": "分享的链接", "Import": "导入", "Install": "安装", "Integrations": "集成", "Revoke token": "吊销 token", "Revoke": "吊销", "Show path to document": "显示文档路径", "Path to document": "文件路径", "Group member options": "群组成员选项", "Export collection": "导出文档集", "Rename": "重命名", "Sort in sidebar": "调整侧边栏的显示顺序", "A-Z sort": "A-Z 排序", "Z-A sort": "Z-A 排序", "Manual sort": "手动排序", "Comment options": "评论选项", "Show document menu": "显示文档菜单", "{{ documentName }} restored": "{{ documentName }} 已被恢复", "Document options": "文档选项", "Choose a collection": "选择一个文档集", "Subscription inherited from collection": "从收藏继承下来的订阅", "Apply template": "应用模板", "Enable embeds": "启用嵌入", "Export options": "导出选项", "Group members": "群组成员", "Edit group": "编辑群组", "Delete group": "删除群组", "Group options": "分组选项", "Cancel": "取消", "Import menu options": "导入菜单选项", "Member options": "成员选项", "New document in <em>{{ collectionName }}</em>": "在 <em>{{ collectionName }}</em> 中新建文档", "New child document": "新建子文档", "Save in workspace": "保存到工作区", "Notification settings": "通知设置", "Revoke {{ appName }}": "撤回 {{ appName }}", "Revoking": "吊销中", "Are you sure you want to revoke access?": "你确定要吊销权限吗？", "Delete app": "删除应用", "Revision options": "修订选项", "Share link revoked": "此分享链接已被撤销", "Share link copied": "分享链接已复制", "Share options": "分享选项", "Go to document": "转到文档", "Revoke link": "撤消链接", "Contents": "目录", "Headings you add to the document will appear here": "添加到文档中的标题将显示在这里", "Table of contents": "目录", "Change name": "更换名称", "Change email": "更换邮箱地址", "Suspend user": "停用用户", "An error occurred while sending the invite": "发送邀请时遇到错误", "User options": "用户选项", "Change role": "更改角色", "Resend invite": "重新发送邀请", "Revoke invite": "撤消邀请", "Activate user": "激活用户", "template": "模板", "document": "文档", "published": "已发布", "edited": "编辑", "created the collection": "文档集已创建", "mentioned you in": "提及你在", "left a comment on": "留下评论在", "resolved a comment on": "回复了一个评论于", "shared": "已共享", "invited you to": "邀请你加入", "Choose a date": "选择一个日期", "API key created. Please copy the value now as it will not be shown again.": "API 密钥已创建。请现在复制值，因为它不会再次显示。", "Scopes": "作用域", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "通过填写空格分割的作用域让此 API 密钥只能访问 API 的特定部分。留空以使其获得对 API 的完全访问权限", "Expiration": "过期时间", "Never expires": "永不过期", "7 days": "7 天", "30 days": "30 天", "60 days": "60 天", "90 days": "90 天", "Custom": "自定义", "No expiration": "无期限", "The document archive is empty at the moment.": "文档归档目前是空的。", "Collection menu": "文档集菜单", "Drop documents to import": "拖放文档以导入", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> 尚未包含任何文档", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} 个用户和 {{ groupsCount }} 个组有访问权限", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} 个用户和 {{ groupsCount }} 个组有访问权限", "{{ usersCount }} users and a group have access": "{{ usersCount }} 个用户和一个组有访问权限", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} 个用户和一个组有访问权限", "{{ usersCount }} users with access": "{{ usersCount }} 个用户有访问权限", "{{ usersCount }} users with access_plural": "{{ usersCount }} 个用户有访问权限", "{{ groupsCount }} groups with access": "{{ groupsCount }} 个组有访问权限", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} 个组有访问权限", "Archived by {{userName}}": "已被 {{userName}} 存档", "Sorry, an error occurred saving the collection": "抱歉，保存文档集时出错", "Add a description": "添加描述", "Share": "分享", "Overview": "概览", "Recently updated": "最近更新", "Recently published": "最近发布", "Least recently updated": "最近最少更新", "A–Z": "A–Z", "Signing in": "登录中", "You can safely close this window once the Outline desktop app has opened": "Outline 桌面应用程序打开后，你可以安全地关闭此窗口", "Error creating comment": "创建评论时出错", "Add a comment": "添加评论", "Add a reply": "增加回复", "Reply": "回复", "Post": "发布", "Upload image": "上传图片", "No resolved comments": "没有已解决的评论", "No comments yet": "暂无评论", "New comments": "新建评论", "Most recent": "最近的", "Order in doc": "按文档中的顺序排序", "Resolved": "已解决", "Sort comments": "排序评论", "Show {{ count }} reply": "显示 {{ count }} 回复", "Show {{ count }} reply_plural": "显示 {{ count }} 回复", "Error updating comment": "更新评论时出错", "Document is too large": "文档太大", "This document has reached the maximum size and can no longer be edited": "此文档已达到最大尺寸，无法再编辑", "Authentication failed": "身份验证失败", "Please try logging out and back in again": "请尝试登出并重新登录", "Authorization failed": "用户授权失败", "You may have lost access to this document, try reloading": "你似乎失去了对此文档的访问权限，请尝试重新加载", "Too many users connected to document": "连接到文档的用户过多", "Your edits will sync once other users leave the document": "你的编辑将在其他用户离开文档后同步", "Server connection lost": "服务器连接断开", "Edits you make will sync once you’re online": "你所做的修改将在你在线后立即同步", "Document restored": "文档已恢复", "Images are still uploading.\nAre you sure you want to discard them?": "图片仍在上传中。\n你确定要丢弃它们吗？", "{{ count }} comment": "{{ count }} 条评论", "{{ count }} comment_plural": "{{ count }} 条评论", "Viewed by": "已被浏览", "only you": "仅你自己", "person": "人", "people": "用户", "Last updated": "最后更新", "Type '/' to insert, or start writing…": "输入'/'来插入，或开始写...", "Hide contents": "隐藏内容", "Show contents": "显示内容", "available when headings are added": "当添加标题时可用", "Edit {{noun}}": "编辑 {{noun}}", "Switch to dark": "切换至深色", "Switch to light": "切换至浅色", "Archived": "已归档", "Save draft": "保存草稿", "Done editing": "保存编辑", "Restore version": "恢复此版本", "No history yet": "暂无历史记录", "Source": "源", "Imported from {{ source }}": "从 {{ source }} 导入", "Stats": "统计信息", "{{ count }} minute read": "已读 {{ count }} 分钟", "{{ count }} minute read_plural": "已读 {{ count }} 分钟", "{{ count }} words": "{{ count }} 个词", "{{ count }} words_plural": "{{ count }} 个词", "{{ count }} characters": "{{ count }} 个字符", "{{ count }} characters_plural": "{{ count }} 个字符", "{{ number }} emoji": "{{ number }} 个表情符号", "No text selected": "没有选定文本", "{{ count }} words selected": "{{ count }} 个词已选择", "{{ count }} words selected_plural": "{{ count }} 个词已选择", "{{ count }} characters selected": "{{ count }} 个字符已选择", "{{ count }} characters selected_plural": "{{ count }} 个字符已选择", "Contributors": "贡献者", "Created": "创建日期", "Creator": "创建者", "Last edited": "最后编辑者", "Previously edited": "上次编辑", "No one else has viewed yet": "还没有其他人浏览过", "Viewed {{ count }} times by {{ teamMembers }} people": "{{ teamMembers }} 人浏览 {{ count }} 次", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "{{ teamMembers }} 人浏览 {{ count }} 次", "Viewer insights are disabled.": "浏览者统计功能已禁用。", "Sorry, the last change could not be persisted – please reload the page": "抱歉，无法保存上次更改 - 请重新载入页面", "{{ count }} days": "{{ count }} 天", "{{ count }} days_plural": "{{ count }} 天", "This template will be permanently deleted in <2></2> unless restored.": "此模板将在 <2></2> 后永久删除，除非手动恢复。", "This document will be permanently deleted in <2></2> unless restored.": "此文档将在 <2></2> 后永久删除，除非手动恢复。", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "选中一些文本，然后通过 <1></1> 来添加占位符，以便在创建新文件时填写。", "You’re editing a template": "你正在编辑一个模板", "Deleted by {{userName}}": "已被 {{userName}} 删除", "Observing {{ userName }}": "观察 {{ userName }}", "Backlinks": "反向链接", "Close": "关闭", "This document is large which may affect performance": "此文档较大，可能会影响性能", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} 正在使用 {{ appName }} 共享文档，请登录以继续。", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "确认删除 <em>{{ documentTitle }}</em> 文档模板？", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "你确定吗？删除 <em>{{ documentTitle }}</em> 文档将一并清除其所有历史记录</em>。", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "你确定吗？删除文档 <em>{{ documentTitle }}</em> 将一并删除其所有历史记录与其包含的 <em>{{ any }} 份子文档</em>。", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "你确定吗？删除文档 <em>{{ documentTitle }}</em> 将一并删除其所有历史记录与其包含的 <em>{{ any }} 份子文档</em>。", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "如果你将来希望引用或还原{{noun}}，请考虑将其存档。", "Select a location to move": "选择要移动的目标位置", "Document moved": "文档已被移动", "Couldn’t move the document, try again?": "无法移动文档，请重试", "Move to <em>{{ location }}</em>": "移动到 <em>{{ location }}</em>", "Couldn’t create the document, try again?": "无法创建文档，请重试？", "Document permanently deleted": "文档已被永久删除", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "确定要永久地删除 <em>{{ documentTitle }}</em> 文档吗？此操作即时生效且无法撤消。", "Select a location to publish": "选择发布位置", "Document published": "文档已发布", "Couldn’t publish the document, try again?": "无法发布文档，再试一次？", "Publish in <em>{{ location }}</em>": "发布到<em>{{ location }}</em>", "Search documents": "搜索文档", "No documents found for your filters.": "没有为你的筛选找到文档。", "You’ve not got any drafts at the moment.": "你目前还没有任何草稿。", "Payment Required": "需要付款", "No access to this doc": "无法访问此文档", "It doesn’t look like you have permission to access this document.": "看起来你没有权限访问这个文档。", "Please request access from the document owner.": "请向文档所有者请求访问权限。", "Not found": "未找到", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "找不到您要找的页面。它可能已被删除或链接不正确。", "Offline": "离线", "We were unable to load the document while offline.": "离线状态无法加载文档。", "Your account has been suspended": "你的账户已被停用", "Warning Sign": "警告标志", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "工作区管理员 (<em>{{ suspendedContactEmail }}</em>) 已停用你的账户。要重新激活你的账户，请直接联系他们。", "Created by me": "由我创建", "Weird, this shouldn’t ever be empty": "奇怪，这里不应该是空的", "You haven’t created any documents yet": "你尚未创建任何文档", "Documents you’ve recently viewed will be here for easy access": "你最近浏览过的文档将放在此处以方便快速访问", "We sent out your invites!": "我们发送了你的邀请！", "Those email addresses are already invited": "这些电子邮箱地址已经被邀请过", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "抱歉，你一次只能发送 {{MAX_INVITES}} 个邀请", "Invited {{roleName}} will receive access to": "被邀请的 {{roleName}} 将获得访问权限对", "{{collectionCount}} collections": "{{collectionCount}} 个文档集", "Admin": "管理员", "Can manage all workspace settings": "可以管理所有工作区设置", "Can create, edit, and delete documents": "可以创建、编辑和删除文档", "Can view and comment": "可以浏览和评论", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "邀请成员或访客加入你的工作区。他们可以使用 {{signinMethods}} 或电子邮箱登录。", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "邀请成员加入你的工作区。他们需要通过 {{signinMethods}} 登录。", "As an admin you can also <2>enable email sign-in</2>.": "作为管理员，你还可以 <2>启用电子邮件登录</2>。", "Invite as": "邀请作为", "Role": "角色", "Email": "邮箱", "Add another": "再添加一个", "Inviting": "邀请中……", "Send Invites": "发送邀请", "Open command menu": "打开命令菜单", "Forward": "向前", "Edit current document": "编辑当前文档", "Move current document": "移动当前文档", "Open document history": "打开文档历史", "Jump to search": "跳转到搜索", "Jump to home": "跳到页", "Focus search input": "聚焦搜索结果", "Open this guide": "打开指南", "Enter": "回车", "Publish document and exit": "发布文档并退出", "Save document": "保存文档", "Cancel editing": "取消编辑", "Collaboration": "协作", "Formatting": "格式化", "Paragraph": "段落", "Large header": "大标题", "Medium header": "中标题", "Small header": "小标题", "Underline": "下划线", "Undo": "撤销", "Redo": "重做", "Lists": "列表", "Toggle task list item": "切换任务列表项", "Tab": "制表符", "Indent list item": "缩进列表项", "Outdent list item": "凸出清单项目", "Move list item up": "上移列表项目", "Move list item down": "下移列表项目", "Tables": "表格", "Insert row": "插入行", "Next cell": "下一个单元格", "Previous cell": "上一个单元格", "Space": "空格", "Numbered list": "编号列表", "Blockquote": "块引用", "Horizontal divider": "水平分隔线", "LaTeX block": "LaTeX 块", "Inline code": "行内代码", "Inline LaTeX": "行内公式", "Triggers": "触发器", "Mention users and more": "提及用户等", "Emoji": "表情", "Insert block": "插入块", "Sign In": "登录", "Continue with Email": "使用电子邮件继续", "Continue with {{ authProviderName }}": "使用 {{ authProviderName }} 继续", "Back to home": "回到主页", "The workspace could not be found": "找不到该工作区", "To continue, enter your workspace’s subdomain.": "要继续，请输入你的工作区的子域。", "subdomain": "子域", "Continue": "继续操作", "The domain associated with your email address has not been allowed for this workspace.": "与你的电子邮件地址相关联的域未被允许在此工作区使用。", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "无法登录。请导航至你工作区的自定义 URL，然后尝试重新登录。<1></1>如果你受邀加入某个工作区，你会在邀请电子邮件中找到指向该工作区的链接。", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "抱歉，不能使用个人 Gmail 地址创建新帐户。<1></1>请改用 Google Workspaces 帐户。", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "与你的用户相关联的工作区已计划删除，目前无法访问。", "The workspace you authenticated with is not authorized on this installation. Try another?": "该工作区没有授权你的身份验证在此安装上。尝试另一个？", "We could not read the user info supplied by your identity provider.": "我们无法读取你的身份验证提供者提供的用户信息。", "Your account uses email sign-in, please sign-in with email to continue.": "你的账户使用电子邮件登录，请使用电子邮件进行登录以继续。", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "最近已发送了一封电子邮件登录链接，请检查你的收件箱或稍后再试。", "Authentication failed – we were unable to sign you in at this time. Please try again.": "身份验证失败 - 我们现在无法让你登录，请重试。", "Authentication failed – you do not have permission to access this workspace.": "身份验证失败 - 你没有访问此工作区的权限。", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "抱歉，看起来该登录链接已经失效，请尝试其他链接。", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "你的账户已被停用。若要重新激活你的账户，请联系工作区管理员。", "This workspace has been suspended. Please contact support to restore access.": "此工作区已暂停。 请联系支持人员以恢复访问权限。", "Authentication failed – this login method was disabled by a workspace admin.": "身份验证失败 - 此登录方法被工作区管理员禁用。", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "你正在尝试加入的工作区需要邀请才能创建账户。<1></1>请向你的工作区管理员申请邀请并重试。", "Sorry, an unknown error occurred.": "抱歉，发生未知错误。", "Choose a workspace": "选择一个工作区", "Choose an {{ appName }} workspace or login to continue connecting this app": "选择 {{ appName }} 工作区或登录以继续连接该应用", "Create workspace": "创建工作区", "Setup your workspace by providing a name and details for admin login. You can change these later.": "通过提供管理员登录的名称和详细信息来设置你的工作区。你可以稍后再修改。", "Workspace name": "工作区名称", "Admin name": "管理员名称", "Admin email": "管理员邮箱", "Login": "登录", "Error": "加载失败", "Failed to load configuration.": "配置文件加载失败。", "Check the network requests and server logs for full details of the error.": "检查网络请求和服务器日志以获取错误的完整详细信息。", "Custom domain setup": "自定义域设置", "Almost there": "即将完成", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "你的自定义域已成功指向 Outline。要完成设置过程，请联系技术支持。", "Choose workspace": "选择工作区", "This login method requires choosing your workspace to continue": "这种登录方法需要选择工作区才能继续", "Check your email": "检查你的电子邮件", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "如果帐户存在，魔法登录链接已发送至电子邮件 <em>{{ emailLinkSentTo }}</em>。", "Back to login": "回到登录界面", "Get started by choosing a sign-in method for your new workspace below…": "先为你的新工作区选择一种登录方法，再继续…", "Login to {{ authProviderName }}": "登录到 {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "你上次登录的方式为 {{ authProviderName }}。", "Or": "或", "Already have an account? Go to <1>login</1>.": "已经有帐户了？前往<1>登录</1>。", "An error occurred": "发生错误", "The OAuth client could not be found, please check the provided client ID": "找不到 OAuth 客户端，请检查提供的客户端 ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "无法加载 OAuth 客户端，请检查回调 URI 是否有效", "Required OAuth parameters are missing": "缺少所需的 OAuth 链接参数", "Authorize": "授权", "{{ appName }} wants to access {{ teamName }}": "应用 {{ appName }} 想要访问 {{ teamName }}", "By <em>{{ developerName }}</em>": "创建人 <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} 将会获得访问你账户的权限，并将可以执行以下操作", "read": "读", "write": "写", "read and write": "读写", "API keys": "API 密钥", "attachments": "附件", "collections": "文档集", "comments": "评论", "documents": "文档", "events": "事件", "groups": "群组", "integrations": "集成", "notifications": "通知", "reactions": "回应", "pins": "固定", "shares": "共享", "users": "用户", "teams": "团队", "workspace": "工作区", "Read all data": "读所有数据", "Write all data": "写所有数据", "Any collection": "文档集", "All time": "所有时间", "Past day": "过去一天", "Past week": "上周", "Past month": "上个月", "Past year": "去年", "Any time": "任何时间", "Remove document filter": "移除文档过滤", "Any status": "任何状态", "Remove search": "移除搜索", "Any author": "任何作者", "Search titles only": "仅搜索标题", "Something went wrong": "出现了一些错误", "Please try again or contact support if the problem persists": "如果问题持续存在，请重试或联系支持人员。", "No documents found for your search filters.": "没有为你的搜索筛选找到文档。", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "创建个人 API 密钥以便通过 API 进行身份验证，并以编程方式管控您工作区的数据。详见 <em>开发者文档</em>。", "API keys have been disabled by an admin for your account": "你账户的 API 密钥已被管理员禁用", "Application access": "应用权限", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "管理第三方及内部应用对您 {{ appName }} 账户的访问权限。", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API 密钥可以用于验证 API 并通过编程的方式控制\n          您的工作区数据。 了解更多详情，请参阅 <em>开发者文档</em>", "Application published": "应用已发布", "Application updated": "应用已更新", "Client secret rotated": "轮换客户端密钥", "Rotate secret": "轮换密钥", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "轮换客户端密钥将会使之前的密钥不再有效。请确保更新使用这些密钥的应用程序。", "Displayed to users when authorizing": "在授权时显示给用户", "Developer information shown to users when authorizing": "在授权时显示给用户的开发者信息", "Developer name": "开发者名称", "Developer URL": "开发者 URL", "Allow users from other workspaces to authorize this app": "允许其他工作区的用户授权此应用", "Credentials": "凭据", "OAuth client ID": "OAuth 客户端 ID", "The public identifier for this app": "此应用的公共标识符", "OAuth client secret": "OAuth 客户端密钥", "Store this value securely, do not expose it publicly": "将在这个值储存在非公开的安全位置", "Where users are redirected after authorizing this app": "用户授权此应用时被重定向到的地址", "Authorization URL": "授权 URL", "Where users are redirected to authorize this app": "用户登录此应用时被重定向到的地址", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "通过应用，您可以为 Outline 构建内部或公开的集成方案，并通过 OAuth 协议实现安全访问。详见<em>开发者文档</em>。", "by {{ name }}": "由 {{ name }}创建", "Last used": "最后使用的", "No expiry": "没有到期", "Restricted scope": "受限作用域", "API key copied to clipboard": "API key 已复制到剪贴板", "Copied": "已复制", "Are you sure you want to revoke the {{ tokenName }} token?": "确认吊销这个 {{ tokenName }} token 吗？", "Disconnect integration": "断开集成", "Connected": "已连接", "Disconnect": "断开连接", "Disconnecting": "正在断开连接", "Allowed domains": "允许的域", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "应该允许使用 SSO 创建新帐户的域。更改此设置不会影响现有用户帐户。", "Remove domain": "删除域", "Add a domain": "添加域", "Save changes": "保存更改", "Please choose a single file to import": "请选择要导入的文件", "Your import is being processed, you can safely leave this page": "你的导入正在处理中，你可以安全地离开此页面", "File not supported – please upload a valid ZIP file": "不支持文件 - 请上传有效的 ZIP 文件", "Set the default permission level for collections created from the import": "为从导入创建的文档集设置默认权限级别", "Uploading": "上传中", "Start import": "开始导入", "Processing": "处理中", "Expired": "已过期", "Completed": "已完成", "Failed": "失败", "All collections": "所有文档集", "Import deleted": "导入已删除", "Export deleted": "导出已删除", "Are you sure you want to delete this import?": "你确定要删除此导入吗？", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "删除此导入也会删除从它创建的所有文档集和文档。此操作无法撤消。", "Check server logs for more details.": "检查服务器日志获取更多详情。", "{{userName}} requested": "{{userName}} 已请求", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "群组用于管理你的团队。当以职能或责任为中心时，工作效果最好 — 例如支持类或工程类。", "You’ll be able to add people to the group next.": "接下来，你将能够将人员添加到群组中。", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "你可以随时编辑该群组的名称，但过于频繁可能会使你的队友感到困惑。", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "你确定吗？删除 <em>{{groupName}}</em> 组将使其成员无法访问它所关联的文档集和文档。", "Add people to {{groupName}}": "添加用户到 {{groupName}}", "{{userName}} was removed from the group": "从群组中移除 {{userName}}。", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "向 <em>{{groupName}}</em> 群组添加和删除成员。该群组的成员将有权访问该组已添加到的任何文档集。", "Add people": "添加人员", "Listing members of the <em>{{groupName}}</em> group.": "列出 <em>{{groupName}}</em> 组的成员。", "This group has no members.": "这个群组没有任何成员。", "{{userName}} was added to the group": "{{userName}} 已添加到群组", "Could not add user": "无法添加用户", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "添加下面的成员可以让他们访问该群组。需要添加一个尚未加入的成员吗？", "Invite them to {{teamName}}": "邀请他们加入 {{teamName}}", "Ask an admin to invite them first": "请管理员先邀请他们。", "Search by name": "按名称搜索", "Search people": "搜索用户", "No people matching your search": "没有符合搜索条件的用户", "No people left to add": "没有人可以添加", "Date created": "创建日期", "Crop Image": "裁剪图像", "Crop image": "裁剪图像", "How does this work?": "这是如何工作的？", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "你可以导入以前从另一个实例中的JSON选项导出的zip文件。在 {{ appName }} 的侧边栏设置中打开 <em>导出</em> ，并点击 <em>导出数据</em>。", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "拖放或单击上传从 {{appName}} 的 JSON 导出选项导出的 zip 文件", "Canceled": "已取消", "Import canceled": "导入已取消", "Are you sure you want to cancel this import?": "您确定要取消此导入吗？", "Canceling": "正在取消", "Canceling this import will discard any progress made. This cannot be undone.": "取消此导入将丢弃所取得的任何进展。此操作无法撤消。", "{{ count }} document imported": "{{ count }} 个文档已导入", "{{ count }} document imported_plural": "{{ count }} 个文档已导入", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "你可以导入以前从 Outline 安装中导出的 zip 文件 – 文档集、文档和图片将会被导入。在 Outline 的侧边栏设置中打开 <em>导出</em> 并单击 <em>导出数据</em>。", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "拖放或单击上传从 {{appName}} 的 Markdown 导出选项导出的 zip 文件", "Configure": "配置", "Connect": "连接", "Last active": "最近活跃", "Guest": "访客", "Never used": "从未被使用过", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "你确定要删除应用 {{ appName }} 吗？此操作不可被撤回。", "Shared by": "共享自", "Date shared": "分享日期", "Last accessed": "上次访问", "Domain": "域", "Views": "浏览次数", "All roles": "所有角色", "Admins": "管理员", "Editors": "编辑者", "All status": "所有状态", "Active": "活跃的", "Left": "左", "Right": "右", "Settings saved": "设置已保存", "Logo updated": "徽标已更新", "Unable to upload new logo": "无法上传新徽标", "Delete workspace": "删除工作区", "These settings affect the way that your workspace appears to everyone on the team.": "这些设置影响团队中每个人的工作区显示方式。", "Display": "显示", "The logo is displayed at the top left of the application.": "徽标显示在应用程序左上角。", "The workspace name, usually the same as your company name.": "工作区名称，通常与你的公司名称相同。", "Description": "描述", "A short description of your workspace.": "你工作区的简短描述。", "Theme": "主题", "Customize the interface look and feel.": "自定义外观和样式。", "Reset theme": "重置主题", "Accent color": "主题色", "Accent text color": "文本强调色", "Public branding": "品牌推广", "Show your workspace logo, description, and branding on publicly shared pages.": "在公开分享的页面上显示你工作区的标志、描述和品牌。", "Table of contents position": "目录位置", "The side to display the table of contents in relation to the main content.": "与主要内容相关的目录的显示位置。", "Behavior": "行为", "Subdomain": "子域", "Your workspace will be accessible at": "你的工作区将在", "Choose a subdomain to enable a login page just for your team.": "选择一个子域以仅为您的团队启用一个登录页面。", "This is the screen that workspace members will first see when they sign in.": "这是工作区成员登录时首先看到的界面。", "Danger": "危险操作", "You can delete this entire workspace including collections, documents, and users.": "你可以删除整个工作区，包括文档集、文档和用户。", "Export data": "导出数据", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "完整导出可能需要一些时间，请考虑导出单个文档或文档集。导出的数据是 Markdown 格式的文档压缩包。当导出已开始时你可以离开此页面。如果你开启了通知，当导出完成后，我们会发送链接至邮箱 <em>{{ userEmail }}</em>。", "Recent exports": "最近导出", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "管理可选功能和测试功能。更改这些设置将影响工作组内所有成员的使用。", "Separate editing": "独立编辑", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "当启用时，默认情况下文档为独立编辑模式，而不总是可编辑的。此设置可以由用户偏好设置覆盖。", "When enabled team members can add comments to documents.": "启用后，团队成员可以向文档添加评论。", "Create a group": "创建群组", "Could not load groups": "无法加载组", "New group": "新建群组", "Groups can be used to organize and manage the people on your team.": "使用群组来组织和管理你的团队成员。", "No groups have been created yet": "没有群组可供加入", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "导入包含 Markdown 文档的 zip 文件 (从 0.67.0 或更早版本导出)", "Import data": "导入数据", "Import a JSON data file exported from another {{ appName }} instance": "导入从另一个 {{ appName }} 实例导出的 JSON 数据文件", "Import pages from a Confluence instance": "从 Confluence 实例导入页面", "Enterprise": "企业服务", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "快速将你现有的文件、页面和文件从其他工具和服务转移到 {{appName}}。你还可以将任何 HTML、Markdown 和文本文件直接拖放到应用程序中的文档集中。", "Recent imports": "最近导入", "Configure a variety of integrations with third-party services.": "配置各种与第三方服务的集成。", "Could not load members": "无法加载成员", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "每个登录到 {{appName}} 的用户都会出现在这里。可能还有其他用户可以通过 {{signinMethods}} 访问，但还尚未登录。", "Receive a notification whenever a new document is published": "每当发布新文档时收到通知", "Document updated": "文档已更新", "Receive a notification when a document you are subscribed to is edited": "每当你订阅的文档被编辑时收到通知", "Comment posted": "注释已发布", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "每当你订阅的文档或参与的话题收到评论时收到通知", "Mentioned": "被提及", "Receive a notification when someone mentions you in a document or comment": "每当有人在文档或评论中提及你时收到通知", "Receive a notification when a comment thread you were involved in is resolved": "当您参与的评论帖子标记为解决时将收到通知", "Collection created": "已创建文档集", "Receive a notification whenever a new collection is created": "每当创建新文档集时收到通知", "Invite accepted": "邀请已被接受", "Receive a notification when someone you invited creates an account": "当你邀请的人创建账号时接收通知", "Invited to document": "被邀请到文档", "Receive a notification when a document is shared with you": "每当有文档共享给你时收到通知", "Invited to collection": "邀请到文档集", "Receive a notification when you are given access to a collection": "当你有权访问文档集时收到通知", "Export completed": "导出已完成", "Receive a notification when an export you requested has been completed": "当你请求的导出任务完成时接收通知", "Getting started": "新手指南", "Tips on getting started with features and functionality": "有关特性和功能的入门提示", "New features": "新特性", "Receive an email when new features of note are added": "当笔记增加新功能时收到电子邮件", "Notifications saved": "通知配置已保存", "Unsubscription successful. Your notification settings were updated": "取消订阅成功。你的通知设置已更新", "Manage when and where you receive email notifications.": "管理你接收电子邮件通知的时间和地点。", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "电子邮件集成当前已禁用。请设置相关的环境变量并重新启动服务器以启用通知。", "Preferences saved": "首选项已保存", "Delete account": "删除账户", "Manage settings that affect your personal experience.": "管理影响你个人体验的设置。", "Language": "语言", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "选择界面语言。我们通过 <2>翻译门户</2> 接受来自社区的翻译。", "Choose your preferred interface color scheme.": "选择你喜欢的界面配色方案。", "Use pointer cursor": "使用指针光标", "Show a hand cursor when hovering over interactive elements.": "将鼠标悬停在交互式元素上时显示手形光标。", "Show line numbers": "显示行号", "Show line numbers on code blocks in documents.": "在文档中的代码块上显示行号。", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "启用时，文档为独立编辑模式。禁用时，文档在你拥有权限时始终可编辑。", "Remember previous location": "记住上一次的位置", "Automatically return to the document you were last viewing when the app is re-opened.": "重新打开应用程序时自动返回到你上次浏览的文档。", "Smart text replacements": "智能文本替换", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "用符号、破折号、 智能引号和其他排版元素替换快捷方式来自动格式化文本。", "You may delete your account at any time, note that this is unrecoverable": "你可以随时删除你的帐户，请注意这不可恢复", "Profile saved": "配置已保存", "Profile picture updated": "头像已更新", "Unable to upload new profile picture": "无法上传个人图片", "Manage how you appear to other members of the workspace.": "管理工作区其他成员的看到你的样式。", "Photo": "图片", "Choose a photo or image to represent yourself.": "选择一张照片或图片作为你的头像。", "This could be your real name, or a nickname — however you’d like people to refer to you.": "这可以是你的真名，也可以是昵称 — 不管你希望别人怎么称呼你。", "Email address": "电子邮件地址", "Are you sure you want to require invites?": "你确定要发出邀请吗？", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "新用户首先需要通过邀请来创建一个账户。 <em>默认角色</em> 和 <em>允许的域</em> 将不再适用。", "Settings that impact the access, security, and content of your workspace.": "影响你工作区中访问权限、安全性和内容的设置。", "Allow members to sign-in with {{ authProvider }}": "允许成员使用 {{ authProvider }} 登录", "Disabled": "已禁用", "Allow members to sign-in using their email address": "允许成员使用他们的电子邮件地址登录", "The server must have SMTP configured to enable this setting": "服务器必须配置 SMTP 以启用此设置", "Access": "访问", "Allow users to send invites": "允许用户发送邀请", "Allow editors to invite other people to the workspace": "允许编辑者邀请其他人到工作区", "Require invites": "需要邀请", "Require members to be invited to the workspace before they can create an account using SSO.": "成员需要先被邀请进入工作区，才能通过SSO创建账户。", "Default role": "默认权限", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "新帐户的默认用户角色。更改此设置不会影响现有用户帐户。", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "启用后，工作区的任何成员都可以在互联网上公开共享文档", "Viewer document exports": "浏览者文档导出", "When enabled, viewers can see download options for documents": "启用时，浏览者可以看到文档的下载选项", "Users can delete account": "用户可以删除账号", "When enabled, users can delete their own account from the workspace": "如果启用，用户可以从工作区删除自己的帐户", "Rich service embeds": "富文本嵌入服务", "Links to supported services are shown as rich embeds within your documents": "受 Outline 服务支持的链接可作为富文本嵌入显示在你的文档中", "Collection creation": "文档集创建", "Allow editors to create new collections within the workspace": "允许成员在工作区中创建新的文档集", "Workspace creation": "工作区已创建", "Allow editors to create new workspaces": "允许编辑者创建新的工作区", "Could not load shares": "无法加载共享", "Sharing is currently disabled.": "共享功能当前被禁用。", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "你可以在<em>安全设置</em> 中对共享文档功能进行全局启用或禁用。", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "这是你分享给其他人的所有文档，在你撤销链接之前，任何人都可以通过该链接阅读该文档。", "You can create templates to help your team create consistent and accurate documentation.": "创建模板来帮助你的团队创建风格一致和准确的文档。", "Alphabetical": "按字母顺序排列", "There are no templates just yet.": "尚无可用模板。", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "验证码已经发送至你的电子邮箱，请在下面输入代码以永久销毁此工作区。", "Confirmation code": "验证码", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "删除 <1>{{workspaceName}}</1> 工作区将销毁所有文档集、文档、用户和关联数据。 您将立即登出 {{appName}}。", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "请注意，工作区是完全分离的。它们可以有不同的域、设置、用户和账单。", "You are creating a new workspace using your current account — <em>{{email}}</em>": "你正在使用当前账户创建新的工作区 —— <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "若要在另一个电子邮件下创建工作区，请从主页注册", "Trash emptied": "回收站为空", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "你确定要永久删除回收站中的所有文档吗？此操作是立即的且无法撤消。", "Recently deleted": "最近删除的", "Trash is empty at the moment.": "回收站为空。", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "验证码已经发送至你的电子邮箱，请在下面输入代码以永久销毁你的账户。", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "你确定吗？注销你的账户将销毁与你的用户相关联的识别数据并且无法撤消。你将立即退出 {{appName}}，并且你的所有 API token 都将被撤销。", "Delete my account": "删除我的帐户", "Today": "今天", "Yesterday": "昨天", "Last week": "上周", "This month": "本月", "Last month": "上月", "This year": "今年", "Expired yesterday": "昨天已过期", "Expired {{ date }}": "{{date}} 已到期", "Expires today": "今天过期", "Expires tomorrow": "明天过期", "Expires {{ date }}": "{{date}} 到期", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "糟糕，你需要在 Slack 中接受权限才能将 {{appName}} 连接到你的团队。再试一次？", "Something went wrong while authenticating your request. Please try logging in again.": "验证你的请求时出现问题。请尝试重新登录。", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "GitHub帐户的所有者已被要求安装 {{githubAppName}} GitHub应用程序。一旦批准，预览将显示各自的链接。", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "通过连接GitHub组织或特定存储库到 {appName}，可以在文档中预览GitHub issues和pull requests。", "Enabled by {{integrationCreatedBy}}": "由 {{integrationCreatedBy}} 启用", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "断开连接后将无法在文档中预览来自此组织的GitHub链接。你确定吗？", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "Github 集成当前已禁用。请设置相关的环境变量并重新启动服务以启用此集成。", "Google Analytics": "谷歌分析", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "添加 Google Analytics 4 Measurement ID 后会将文档访问量和分析从工作区发送到你自己的 Google Analytics 帐户。", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "在你的 Google Analytics 管理仪表板中创建一个 “Web” 流，从生成的代码片段中可以找到 Measurement ID。", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "糟糕，你需要在 Linear 中接受权限才能将 {{appName}} 连接到你的工作区。再试一次？", "Something went wrong while processing your request. Please try again.": "处理你的请求时出错。请重试。", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "将一个 Linear 工作区连接到 {appName} 从而可以在文档中预览 Linear Issues。", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "断开链接将会使此工作区中文档中的 Linear 链接不再可以被预览。你确定吗？", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "Linear 集成已被禁用。请设置相关的环境变量并重新启动服务以启用此集成。", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "配置一个 Matomo 安装，将视图和分析从工作区发送到你的 Matomo 实例。", "Instance URL": "实例 URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "你的 Matomo 实例 URL。如果你使用 Matomo 云，它将在 matomo.cloud/ 中结束。", "Site ID": "站点 ID", "An ID that uniquely identifies the website in your Matomo instance.": "在你的 Matomo 实例中识别网站的唯一ID。", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "糟糕，您需要接受 Notion 中的权限才能将 {{ appName }} 连接到您的工作区。再试一次？", "Import pages from Notion": "从 Notion 导入页面", "Add to Slack": "添加到 Slack", "document published": "文档已发布", "document updated": "文档已更新", "Posting to the <em>{{ channelName }}</em> channel on": "发布到 <em>{{ channelName }}</em> 频道", "These events should be posted to Slack": "这些事件应该发布到 Slack", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "这将阻止将来的任何更新被发布到此Slack频道。你确定吗？", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "糟糕，你需要在 Slack 中接受权限才能将 {{appName}} 连接到你的工作区。再试一次？", "Personal account": "个人账号", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "将你的 {{appName}} 帐户链接到Slack，以便直接在聊天中搜索和预览你可以访问的文档。", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "断开你的个人帐户会无法从Slack搜索文档。你确定吗？", "Slash command": "斜杠命令", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "获取 Slack 中共享的 {{ appName }} 预览链接，并使用 <em>{{ command }}</em> 斜杠命令搜索文档而无需离开聊天。", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "这将从你的 Slack 工作区中移除 Outline 斜杠命令。你确定吗?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "将 {{appName}} 文档集连接到 Slack 频道，当文档发布或更新时，消息将自动发布到 Slack。", "Comment by {{ author }} on \"{{ title }}\"": "{{ author }} 对 \"{{ title }}\" 的评论", "How to use {{ command }}": "如何使用 {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "要搜索你的工作区，请使用 {{ command }} 命令。\n输入 {{ command2 }} help以显示此帮助文本。", "Post to Channel": "发布到频道…", "This is what we found for \"{{ term }}\"": "在 \"{{ term }}\" 中检索到", "No results for \"{{ term }}\"": "“{{ term }}” 没有结果", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "看起来你还没有将你的 {{ appName }} 帐户连接到Slack", "Link your account": "关联你的帐户", "Link your account in {{ appName }} settings to search from Slack": "在 {{ appName }} 设置中连接你的帐户以从Slack中搜索", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "配置 Umami 安装以将视图和分析从工作区发送到您自己的 Umami 实例。", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "Umami 实例的 URL。如果你使用 Umami Cloud，它将以 {{ url }} 开始", "Script name": "脚本名称", "The name of the script file that Umami uses to track analytics.": "Umami 用于跟踪分析的脚本文件名称。", "An ID that uniquely identifies the website in your Umami instance.": "在您的 Umami 实例中识别网站的唯一ID。", "Are you sure you want to delete the {{ name }} webhook?": "你确定要删除这个 {{ name }} webhook 吗？", "Webhook updated": "Webhook 已更新", "Update": "更新", "Updating": "正在更新", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "提供此 Webhook 的描述性名称以及创建匹配事件时我们应向其发送 POST 请求的 URL。", "A memorable identifer": "一个容易记的名称", "URL": "URL", "Signing secret": "签名密钥", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "订阅所有事件、群组或单个事件。我们建议你只订阅应用程序运行所需的最少数量的事件。", "All events": "全部事件", "All {{ groupName }} events": "全部 {{ groupName }} 事件", "Delete webhook": "删除 Webhook", "Subscribed events": "订阅的事件", "Edit webhook": "编辑 Webhook", "Webhook created": "已创建 Webhook", "Webhooks": "Webhooks", "New webhook": "新建 Webhook", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhooks 可以用来在 {{appName}} 中发生事件时通知你的应用程序。事件作为带有 JSON 负载的 https 请求近乎实时地发送。", "Inactive": "未激活", "Create a webhook": "创建一个 Webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier 是一个平台，可以让 {{appName}} 轻松地与数以千计的其他业务工具集成。自动化你的工作流程、同步数据等。", "Never logged in": "从未登录", "Online now": "当前在线", "Online {{ timeAgo }}": "{{ timeAgo }} 在线", "Viewed just now": "刚刚浏览过", "You updated {{ timeAgo }}": "{{ timeAgo }} 由你更新", "{{ user }} updated {{ timeAgo }}": "{{ timeAgo }} 由 {{ user }} 更新", "You created {{ timeAgo }}": "{{ timeAgo }} 由你创建", "{{ user }} created {{ timeAgo }}": "{{ timeAgo }} 由 {{ user }} 创建", "Error loading data": "加载数据时出错"}