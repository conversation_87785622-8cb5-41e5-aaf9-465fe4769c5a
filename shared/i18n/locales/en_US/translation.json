{"New API key": "New API key", "Open collection": "Open collection", "New collection": "New collection", "Create a collection": "Create a collection", "Edit": "Edit", "Edit collection": "Edit collection", "Permissions": "Permissions", "Collection permissions": "Collection permissions", "Share this collection": "Share this collection", "Search in collection": "Search in collection", "Star": "Star", "Unstar": "Unstar", "Subscribe": "Subscribe", "Subscribed to document notifications": "Subscribed to document notifications", "Unsubscribe": "Unsubscribe", "Unsubscribed from document notifications": "Unsubscribed from document notifications", "Archive": "Archive", "Archive collection": "Archive collection", "Collection archived": "Collection archived", "Archiving": "Archiving", "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.": "Archiving this collection will also archive all documents within it. Documents from the collection will no longer be visible in search results.", "Restore": "Rest<PERSON>", "Collection restored": "Collection restored", "Delete": "Delete", "Delete collection": "Delete collection", "New template": "New template", "Delete comment": "Delete comment", "Mark as resolved": "<PERSON> as resolved", "Thread resolved": "Thread resolved", "Mark as unresolved": "<PERSON> as unresolved", "View reactions": "View reactions", "Reactions": "Reactions", "Copy ID": "Copy ID", "Clear IndexedDB cache": "Clear IndexedDB cache", "IndexedDB cache cleared": "IndexedDB cache cleared", "Toggle debug logging": "Toggle debug logging", "Debug logging enabled": "Debug logging enabled", "Debug logging disabled": "Debug logging disabled", "Development": "Development", "Open document": "Open document", "New document": "New document", "New draft": "New draft", "New from template": "New from template", "New nested document": "New nested document", "Publish": "Publish", "Published {{ documentName }}": "Published {{ documentName }}", "Publish document": "Publish document", "Unpublish": "Unpublish", "Unpublished {{ documentName }}": "Unpublished {{ documentName }}", "Share this document": "Share this document", "HTML": "HTML", "PDF": "PDF", "Exporting": "Exporting", "Markdown": "<PERSON><PERSON>", "Download": "Download", "Download document": "Download document", "Copy as Markdown": "<PERSON><PERSON> as <PERSON><PERSON>", "Markdown copied to clipboard": "Markdown copied to clipboard", "Copy as text": "Copy as text", "Text copied to clipboard": "Text copied to clipboard", "Copy public link": "Copy public link", "Link copied to clipboard": "Link copied to clipboard", "Copy link": "Copy link", "Copy": "Copy", "Duplicate": "Duplicate", "Duplicate document": "Duplicate document", "Copy document": "Copy document", "collection": "collection", "Pin to {{collectionName}}": "Pin to {{collectionName}}", "Pinned to collection": "Pinned to collection", "Pin to home": "Pin to home", "Pinned to home": "Pinned to home", "Pin": "<PERSON>n", "Search in document": "Search in document", "Print": "Print", "Print document": "Print document", "Import document": "Import document", "Templatize": "Templatize", "Create template": "Create template", "Open random document": "Open random document", "Search documents for \"{{searchQuery}}\"": "Search documents for \"{{searchQuery}}\"", "Move to workspace": "Move to workspace", "Move": "Move", "Move to collection": "Move to collection", "Move {{ documentType }}": "Move {{ documentType }}", "Are you sure you want to archive this document?": "Are you sure you want to archive this document?", "Document archived": "Document archived", "Archiving this document will remove it from the collection and search results.": "Archiving this document will remove it from the collection and search results.", "Delete {{ documentName }}": "Delete {{ documentName }}", "Permanently delete": "Permanently delete", "Permanently delete {{ documentName }}": "Permanently delete {{ documentName }}", "Empty trash": "Empty trash", "Permanently delete documents in trash": "Permanently delete documents in trash", "Comments": "Comments", "History": "History", "Insights": "Insights", "Disable viewer insights": "Disable viewer insights", "Enable viewer insights": "Enable viewer insights", "Leave document": "Leave document", "You have left the shared document": "You have left the shared document", "Could not leave document": "Could not leave document", "Home": "Home", "Drafts": "Drafts", "Search": "Search", "Trash": "Trash", "Settings": "Settings", "Profile": "Profile", "Templates": "Templates", "Notifications": "Notifications", "Preferences": "Preferences", "Documentation": "Documentation", "API documentation": "API documentation", "Toggle sidebar": "Toggle sidebar", "Send us feedback": "Send us feedback", "Report a bug": "Report a bug", "Changelog": "Changelog", "Keyboard shortcuts": "Keyboard shortcuts", "Download {{ platform }} app": "Download {{ platform }} app", "Log out": "Log out", "Mark notifications as read": "Mark notifications as read", "Archive all notifications": "Archive all notifications", "New App": "New App", "New Application": "New Application", "This version of the document was deleted": "This version of the document was deleted", "Link copied": "Link copied", "Dark": "Dark", "Light": "Light", "System": "System", "Appearance": "Appearance", "Change theme": "Change theme", "Change theme to": "Change theme to", "Switch workspace": "Switch workspace", "Select a workspace": "Select a workspace", "New workspace": "New workspace", "Create a workspace": "Create a workspace", "Login to workspace": "Login to workspace", "Invite people": "Invite people", "Invite to workspace": "Invite to workspace", "Promote to {{ role }}": "Promote to {{ role }}", "Demote to {{ role }}": "Demote to {{ role }}", "Update role": "Update role", "Delete user": "Delete user", "Collection": "Collection", "Collections": "Collections", "Debug": "Debug", "Document": "Document", "Documents": "Documents", "Recently viewed": "Recently viewed", "Revision": "Revision", "Navigation": "Navigation", "Notification": "Notification", "People": "People", "Workspace": "Workspace", "Recent searches": "Recent searches", "currently editing": "currently editing", "currently viewing": "currently viewing", "previously edited": "previously edited", "You": "You", "Viewers": "Viewers", "Collections are used to group documents and choose permissions": "Collections are used to group documents and choose permissions", "Name": "Name", "The default access for workspace members, you can share with more users or groups later.": "The default access for workspace members, you can share with more users or groups later.", "Public document sharing": "Public document sharing", "Allow documents within this collection to be shared publicly on the internet.": "Allow documents within this collection to be shared publicly on the internet.", "Commenting": "Commenting", "Allow commenting on documents within this collection.": "Allow commenting on documents within this collection.", "Saving": "Saving", "Save": "Save", "Creating": "Creating", "Create": "Create", "Collection deleted": "Collection deleted", "I’m sure – Delete": "I’m sure – Delete", "Deleting": "Deleting", "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.": "Are you sure about that? Deleting the <em>{{collectionName}}</em> collection is permanent and cannot be restored, however all published documents within will be moved to the trash.", "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.": "Also, <em>{{collectionName}}</em> is being used as the start view – deleting it will reset the start view to the Home page.", "Type a command or search": "Type a command or search", "Choose a template": "Choose a template", "Are you sure you want to permanently delete this entire comment thread?": "Are you sure you want to permanently delete this entire comment thread?", "Are you sure you want to permanently delete this comment?": "Are you sure you want to permanently delete this comment?", "Confirm": "Confirm", "manage access": "manage access", "view and edit access": "view and edit access", "view only access": "view only access", "no access": "no access", "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection": "You do not have permission to move {{ documentName }} to the {{ collectionName }} collection", "Move document": "Move document", "Moving": "Moving", "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.": "Moving the document <em>{{ title }}</em> to the {{ newCollectionName }} collection will change permission for all workspace members from <em>{{ prevPermission }}</em> to <em>{{ newPermission }}</em>.", "Submenu": "Submenu", "Collections could not be loaded, please reload the app": "Collections could not be loaded, please reload the app", "Default collection": "Default collection", "Start view": "Start view", "Install now": "Install now", "Deleted Collection": "Deleted Collection", "Untitled": "Untitled", "Unpin": "Unpin", "{{ minutes }}m read": "{{ minutes }}m read", "Select a location to copy": "Select a location to copy", "Document copied": "Document copied", "Couldn’t copy the document, try again?": "Couldn’t copy the document, try again?", "Include nested documents": "Include nested documents", "Copy to <em>{{ location }}</em>": "Copy to <em>{{ location }}</em>", "Search collections & documents": "Search collections & documents", "No results found": "No results found", "New": "New", "Only visible to you": "Only visible to you", "Draft": "Draft", "Template": "Template", "You updated": "You updated", "{{ userName }} updated": "{{ userName }} updated", "You deleted": "You deleted", "{{ userName }} deleted": "{{ userName }} deleted", "You archived": "You archived", "{{ userName }} archived": "{{ userName }} archived", "Imported": "Imported", "You created": "You created", "{{ userName }} created": "{{ userName }} created", "You published": "You published", "{{ userName }} published": "{{ userName }} published", "Never viewed": "Never viewed", "Viewed": "Viewed", "in": "in", "nested document": "nested document", "nested document_plural": "nested documents", "{{ total }} task": "{{ total }} task", "{{ total }} task_plural": "{{ total }} tasks", "{{ completed }} task done": "{{ completed }} task done", "{{ completed }} task done_plural": "{{ completed }} tasks done", "{{ completed }} of {{ total }} tasks": "{{ completed }} of {{ total }} tasks", "Currently editing": "Currently editing", "Currently viewing": "Currently viewing", "Viewed {{ timeAgo }}": "Viewed {{ timeAgo }}", "Module failed to load": "Module failed to load", "Loading Failed": "Loading Failed", "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.": "Sorry, part of the application failed to load. This may be because it was updated since you opened the tab or because of a failed network request. Please try reloading.", "Reload": "Reload", "Something Unexpected Happened": "Something Unexpected Happened", "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.": "Sorry, an unrecoverable error occurred{{notified}}. Please try reloading the page, it may have been a temporary glitch.", "our engineers have been notified": "our engineers have been notified", "Show detail": "Show detail", "Revision deleted": "Revision deleted", "Current version": "Current version", "{{userName}} edited": "{{userName}} edited", "{{userName}} archived": "{{userName}} archived", "{{userName}} restored": "{{userName}} restored", "{{userName}} deleted": "{{userName}} deleted", "{{userName}} added {{addedUserName}}": "{{userName}} added {{addedUserName}}", "{{userName}} removed {{removedUserName}}": "{{userName}} removed {{removedUserName}}", "{{userName}} moved from trash": "{{userName}} moved from trash", "{{userName}} published": "{{userName}} published", "{{userName}} unpublished": "{{userName}} unpublished", "{{userName}} moved": "{{userName}} moved", "Export started": "Export started", "Your file will be available in {{ location }} soon": "Your file will be available in {{ location }} soon", "View": "View", "A ZIP file containing the images, and documents in the Markdown format.": "A ZIP file containing the images, and documents in the Markdown format.", "A ZIP file containing the images, and documents as HTML files.": "A ZIP file containing the images, and documents as HTML files.", "Structured data that can be used to transfer data to another compatible {{ appName }} instance.": "Structured data that can be used to transfer data to another compatible {{ appName }} instance.", "Export": "Export", "Exporting the collection <em>{{collectionName}}</em> may take some time.": "Exporting the collection <em>{{collectionName}}</em> may take some time.", "You will receive an email when it's complete.": "You will receive an email when it's complete.", "Include attachments": "Include attachments", "Including uploaded images and files in the exported data": "Including uploaded images and files in the exported data", "{{count}} more user": "{{count}} more user", "{{count}} more user_plural": "{{count}} more users", "Filter": "Filter", "No results": "No results", "{{authorName}} created <3></3>": "{{authorName}} created <3></3>", "{{authorName}} opened <3></3>": "{{authorName}} opened <3></3>", "Search emoji": "Search emoji", "Search icons": "Search icons", "Choose default skin tone": "Choose default skin tone", "Show menu": "Show menu", "Icon Picker": "<PERSON><PERSON> Picker", "Icons": "Icons", "Emojis": "Emojis", "Remove": "Remove", "All": "All", "Frequently Used": "Frequently Used", "Search Results": "Search Results", "Smileys & People": "Smileys & People", "Animals & Nature": "Animals & Nature", "Food & Drink": "Food & Drink", "Activity": "Activity", "Travel & Places": "Travel & Places", "Objects": "Objects", "Symbols": "Symbols", "Flags": "Flags", "Select a color": "Select a color", "Loading": "Loading", "Permission": "Permission", "View only": "View only", "Can edit": "Can edit", "No access": "No access", "Default access": "Default access", "Change Language": "Change Language", "Dismiss": "<PERSON><PERSON><PERSON>", "You’re offline.": "You’re offline.", "Sorry, an error occurred.": "Sorry, an error occurred.", "Click to retry": "Click to retry", "Back": "Back", "Unknown": "Unknown", "Mark all as read": "Mark all as read", "You're all caught up": "You're all caught up", "Icon": "Icon", "My App": "My App", "Tagline": "Tagline", "A short description": "A short description", "Callback URLs": "Callback URLs", "Published": "Published", "Allow this app to be installed by other workspaces": "Allow this app to be installed by other workspaces", "{{ username }} reacted with {{ emoji }}": "{{ username }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}": "{{ firstUsername }} and {{ secondUsername }} reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}": "{{ firstUsername }} and {{ count }} other reacted with {{ emoji }}", "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}_plural": "{{ firstUsername }} and {{ count }} others reacted with {{ emoji }}", "Add reaction": "Add reaction", "Reaction picker": "Reaction picker", "Could not load reactions": "Could not load reactions", "Reaction": "Reaction", "Results": "Results", "No results for {{query}}": "No results for {{query}}", "Manage": "Manage", "All members": "All members", "Everyone in the workspace": "Everyone in the workspace", "{{ count }} member": "{{ count }} member", "{{ count }} member_plural": "{{ count }} members", "Invite": "Invite", "{{ userName }} was added to the collection": "{{ userName }} was added to the collection", "{{ count }} people added to the collection": "{{ count }} people added to the collection", "{{ count }} people added to the collection_plural": "{{ count }} people added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection": "{{ count }} people and {{ count2 }} groups added to the collection", "{{ count }} people and {{ count2 }} groups added to the collection_plural": "{{ count }} people and {{ count2 }} groups added to the collection", "Add": "Add", "Add or invite": "Add or invite", "Viewer": "Viewer", "Editor": "Editor", "Suggestions for invitation": "Suggestions for invitation", "No matches": "No matches", "Can view": "Can view", "Everyone in the collection": "Everyone in the collection", "You have full access": "You have full access", "Created the document": "Created the document", "Other people": "Other people", "Other workspace members may have access": "Other workspace members may have access", "This document may be shared with more workspace members through a parent document or collection you do not have access to": "This document may be shared with more workspace members through a parent document or collection you do not have access to", "Access inherited from collection": "Access inherited from collection", "{{ userName }} was removed from the document": "{{ userName }} was removed from the document", "Could not remove user": "Could not remove user", "Permissions for {{ userName }} updated": "Permissions for {{ userName }} updated", "Could not update user": "Could not update user", "Has access through <2>parent</2>": "Has access through <2>parent</2>", "Suspended": "Suspended", "Invited": "Invited", "Active <1></1> ago": "Active <1></1> ago", "Never signed in": "Never signed in", "Leave": "Leave", "Only lowercase letters, digits and dashes allowed": "Only lowercase letters, digits and dashes allowed", "Sorry, this link has already been used": "Sorry, this link has already been used", "Public link copied to clipboard": "Public link copied to clipboard", "Web": "Web", "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared": "Anyone with the link can access because the parent document, <2>{{documentTitle}}</2>, is shared", "Allow anyone with the link to access": "Allow anyone with the link to access", "Publish to internet": "Publish to internet", "Search engine indexing": "Search engine indexing", "Disable this setting to discourage search engines from indexing the page": "Disable this setting to discourage search engines from indexing the page", "Show last modified": "Show last modified", "Display the last modified timestamp on the shared page": "Display the last modified timestamp on the shared page", "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future": "Nested documents are not shared on the web. Toggle sharing to enable access, this will be the default behavior in the future", "{{ userName }} was added to the document": "{{ userName }} was added to the document", "{{ count }} people added to the document": "{{ count }} people added to the document", "{{ count }} people added to the document_plural": "{{ count }} people added to the document", "{{ count }} groups added to the document": "{{ count }} groups added to the document", "{{ count }} groups added to the document_plural": "{{ count }} groups added to the document", "Logo": "Logo", "Archived collections": "Archived collections", "New doc": "New doc", "Empty": "Empty", "Collapse": "Collapse", "Expand": "Expand", "Document not supported – try Markdown, Plain text, HTML, or Word": "Document not supported – try Markdown, Plain text, HTML, or Word", "Go back": "Go back", "Go forward": "Go forward", "Could not load shared documents": "Could not load shared documents", "Shared with me": "Shared with me", "Show more": "Show more", "Could not load starred documents": "Could not load starred documents", "Starred": "Starred", "Up to date": "Up to date", "{{ releasesBehind }} versions behind": "{{ releasesBehind }} version behind", "{{ releasesBehind }} versions behind_plural": "{{ releasesBehind }} versions behind", "Change permissions?": "Change permissions?", "{{ documentName }} cannot be moved within {{ parentDocumentName }}": "{{ documentName }} cannot be moved within {{ parentDocumentName }}", "You can't reorder documents in an alphabetically sorted collection": "You can't reorder documents in an alphabetically sorted collection", "The {{ documentName }} cannot be moved here": "The {{ documentName }} cannot be moved here", "Return to App": "Back to App", "Installation": "Installation", "Unstar document": "Unstar document", "Star document": "Star document", "Template created, go ahead and customize it": "Template created, go ahead and customize it", "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.": "Creating a template from <em>{{titleWithDefault}}</em> is a non-destructive action – we'll make a copy of the document and turn it into a template that can be used as a starting point for new documents.", "Enable other members to use the template immediately": "Enable other members to use the template immediately", "Location": "Location", "Admins can manage the workspace and access billing.": "Admins can manage the workspace and access billing.", "Editors can create, edit, and comment on documents.": "Editors can create, edit, and comment on documents.", "Viewers can only view and comment on documents.": "Viewers can only view and comment on documents.", "Are you sure you want to make {{ userName }} a {{ role }}?": "Are you sure you want to make {{ userName }} a {{ role }}?", "I understand, delete": "I understand, delete", "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.": "Are you sure you want to permanently delete {{ userName }}? This operation is unrecoverable, consider suspending the user instead.", "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.": "Are you sure you want to suspend {{ userName }}? Suspended users will be prevented from logging in.", "New name": "New name", "Name can't be empty": "Name can't be empty", "Check your email to verify the new address.": "Check your email to verify the new address.", "The email will be changed once verified.": "The email will be changed once verified.", "You will receive an email to verify your new address. It must be unique in the workspace.": "You will receive an email to verify your new address. It must be unique in the workspace.", "A confirmation email will be sent to the new address before it is changed.": "A confirmation email will be sent to the new address before it is changed.", "New email": "New email", "Email can't be empty": "Email can't be empty", "Your import completed": "Your import completed", "Previous match": "Previous match", "Next match": "Next match", "Find and replace": "Find and replace", "Find": "Find", "Match case": "Match case", "Enable regex": "Enable regex", "Replace options": "Replace options", "Replacement": "Replacement", "Replace": "Replace", "Replace all": "Replace all", "Profile picture": "Profile picture", "Create a new doc": "Create a new doc", "{{ userName }} won't be notified, as they do not have access to this document": "{{ userName }} won't be notified, as they do not have access to this document", "Keep as link": "Keep as link", "Mention": "Mention", "Embed": "Embed", "Add column after": "Add column after", "Add column before": "Add column before", "Add row after": "Add row after", "Add row before": "Add row before", "Align center": "Align center", "Align left": "<PERSON><PERSON> left", "Align right": "Align right", "Default width": "Default width", "Full width": "Full width", "Bulleted list": "Bulleted list", "Todo list": "Task list", "Code block": "Code block", "Copied to clipboard": "Copied to clipboard", "Code": "Code", "Comment": "Comment", "Create link": "Create link", "Sorry, an error occurred creating the link": "Sorry, an error occurred creating the link", "Create a new child doc": "Create a new child doc", "Delete table": "Delete table", "Delete file": "Delete file", "Width x Height": "Width x Height", "Download file": "Download file", "Replace file": "Replace file", "Delete image": "Delete image", "Download image": "Download image", "Replace image": "Replace image", "Italic": "Italic", "Sorry, that link won’t work for this embed type": "Sorry, that link won’t work for this embed type", "File attachment": "File attachment", "Enter a link": "Enter a link", "Big heading": "Big heading", "Medium heading": "Medium heading", "Small heading": "Small heading", "Extra small heading": "Extra small heading", "Heading": "Heading", "Divider": "Divider", "Image": "Image", "Sorry, an error occurred uploading the file": "Sorry, an error occurred uploading the file", "Write a caption": "Write a caption", "Info": "Info", "Info notice": "Info notice", "Link": "Link", "Highlight": "Highlight", "Type '/' to insert": "Type '/' to insert", "Keep typing to filter": "Keep typing to filter", "Open link": "Open link", "Go to link": "Go to link", "Sorry, that type of link is not supported": "Sorry, that type of link is not supported", "Ordered list": "Ordered list", "Page break": "Page break", "Paste a link": "Paste a link", "Paste a {{service}} link…": "Paste a {{service}} link…", "Placeholder": "Placeholder", "Quote": "Quote", "Remove link": "Remove link", "Search or paste a link": "Search or paste a link", "Strikethrough": "Strikethrough", "Bold": "Bold", "Subheading": "Subheading", "Sort ascending": "Sort ascending", "Sort descending": "Sort descending", "Table": "Table", "Export as CSV": "Export as CSV", "Toggle header": "Toggle header", "Math inline (LaTeX)": "Math inline (LaTeX)", "Math block (LaTeX)": "Math block (LaTeX)", "Merge cells": "Merge cells", "Split cell": "Split cell", "Tip": "Tip", "Tip notice": "Tip notice", "Warning": "Warning", "Warning notice": "Warning notice", "Success": "Success", "Success notice": "Success notice", "Current date": "Current date", "Current time": "Current time", "Current date and time": "Current date and time", "Indent": "Indent", "Outdent": "Outdent", "Video": "Video", "None": "None", "Could not import file": "Could not import file", "Unsubscribed from document": "Unsubscribed from document", "Unsubscribed from collection": "Unsubscribed from collection", "Account": "Account", "API & Apps": "API & Apps", "Details": "Details", "Security": "Security", "Features": "Features", "Members": "Members", "Groups": "Groups", "API Keys": "API Keys", "Applications": "Applications", "Shared Links": "Shared Links", "Import": "Import", "Install": "Install", "Integrations": "Integrations", "Revoke token": "Revoke token", "Revoke": "Revoke", "Show path to document": "Show path to document", "Path to document": "Path to document", "Group member options": "Group member options", "Export collection": "Export collection", "Rename": "<PERSON><PERSON>", "Sort in sidebar": "Sort in sidebar", "A-Z sort": "A-Z sort", "Z-A sort": "Z-A sort", "Manual sort": "Manual sort", "Comment options": "Comment options", "Show document menu": "Show document menu", "{{ documentName }} restored": "{{ documentName }} restored", "Document options": "Document options", "Choose a collection": "Choose a collection", "Subscription inherited from collection": "Subscription inherited from collection", "Apply template": "Apply template", "Enable embeds": "Enable embeds", "Export options": "Export options", "Group members": "Group members", "Edit group": "Edit group", "Delete group": "Delete group", "Group options": "Group options", "Cancel": "Cancel", "Import menu options": "Import menu options", "Member options": "Member options", "New document in <em>{{ collectionName }}</em>": "New document in <em>{{ collectionName }}</em>", "New child document": "New child document", "Save in workspace": "Save in workspace", "Notification settings": "Notification settings", "Revoke {{ appName }}": "Revoke {{ appName }}", "Revoking": "Revoking", "Are you sure you want to revoke access?": "Are you sure you want to revoke access?", "Delete app": "Delete app", "Revision options": "Revision options", "Share link revoked": "Share link revoked", "Share link copied": "Share link copied", "Share options": "Share options", "Go to document": "Go to document", "Revoke link": "Revoke link", "Contents": "Contents", "Headings you add to the document will appear here": "Headings you add to the document will appear here", "Table of contents": "Table of contents", "Change name": "Change name", "Change email": "Change email", "Suspend user": "Suspend user", "An error occurred while sending the invite": "An error occurred while sending the invite", "User options": "User options", "Change role": "Change role", "Resend invite": "Resend invite", "Revoke invite": "Revoke invite", "Activate user": "Activate user", "template": "template", "document": "document", "published": "published", "edited": "edited", "created the collection": "created the collection", "mentioned you in": "mentioned you in", "left a comment on": "left a comment on", "resolved a comment on": "resolved a comment on", "shared": "shared", "invited you to": "invited you to", "Choose a date": "Choose a date", "API key created. Please copy the value now as it will not be shown again.": "API key created. Please copy the value now as it will not be shown again.", "Scopes": "<PERSON><PERSON><PERSON>", "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access": "Space-separated scopes restrict the access of this API key to specific parts of the API. Leave blank for full access", "Expiration": "Expiration", "Never expires": "Never expires", "7 days": "7 days", "30 days": "30 days", "60 days": "60 days", "90 days": "90 days", "Custom": "Custom", "No expiration": "No expiration", "The document archive is empty at the moment.": "The document archive is empty at the moment.", "Collection menu": "Collection menu", "Drop documents to import": "Drop documents to import", "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.": "<em>{{ collectionName }}</em> doesn’t contain any\n                    documents yet.", "{{ usersCount }} users and {{ groupsCount }} groups with access": "{{ usersCount }} user and {{ groupsCount }} groups with access", "{{ usersCount }} users and {{ groupsCount }} groups with access_plural": "{{ usersCount }} users and {{ groupsCount }} groups with access", "{{ usersCount }} users and a group have access": "{{ usersCount }} user and a group have access", "{{ usersCount }} users and a group have access_plural": "{{ usersCount }} users and a group have access", "{{ usersCount }} users with access": "{{ usersCount }} user with access", "{{ usersCount }} users with access_plural": "{{ usersCount }} users with access", "{{ groupsCount }} groups with access": "{{ groupsCount }} group with access", "{{ groupsCount }} groups with access_plural": "{{ groupsCount }} groups with access", "Archived by {{userName}}": "Archived by {{userName}}", "Sorry, an error occurred saving the collection": "Sorry, an error occurred saving the collection", "Add a description": "Add a description", "Share": "Share", "Overview": "Overview", "Recently updated": "Recently updated", "Recently published": "Recently published", "Least recently updated": "Least recently updated", "A–Z": "A–Z", "Signing in": "Signing in", "You can safely close this window once the Outline desktop app has opened": "You can safely close this window once the Outline desktop app has opened", "Error creating comment": "Error creating comment", "Add a comment": "Add a comment", "Add a reply": "Add a reply", "Reply": "Reply", "Post": "Post", "Upload image": "Upload image", "No resolved comments": "No resolved comments", "No comments yet": "No comments yet", "New comments": "New comments", "Most recent": "Most recent", "Order in doc": "Order in doc", "Resolved": "Resolved", "Sort comments": "Sort comments", "Show {{ count }} reply": "Show {{ count }} reply", "Show {{ count }} reply_plural": "Show {{ count }} replies", "Error updating comment": "Error updating comment", "Document is too large": "Document is too large", "This document has reached the maximum size and can no longer be edited": "This document has reached the maximum size and can no longer be edited", "Authentication failed": "Authentication failed", "Please try logging out and back in again": "Please try logging out and back in again", "Authorization failed": "Authorization failed", "You may have lost access to this document, try reloading": "You may have lost access to this document, try reloading", "Too many users connected to document": "Too many users connected to document", "Your edits will sync once other users leave the document": "Your edits will sync once other users leave the document", "Server connection lost": "Server connection lost", "Edits you make will sync once you’re online": "Edits you make will sync once you’re online", "Document restored": "Document restored", "Images are still uploading.\nAre you sure you want to discard them?": "Images are still uploading.\nAre you sure you want to discard them?", "{{ count }} comment": "{{ count }} comment", "{{ count }} comment_plural": "{{ count }} comments", "Viewed by": "Viewed by", "only you": "only you", "person": "person", "people": "people", "Last updated": "Last updated", "Type '/' to insert, or start writing…": "Type '/' to insert, or start writing…", "Hide contents": "Hide contents", "Show contents": "Show contents", "available when headings are added": "available when headings are added", "Edit {{noun}}": "Edit {{noun}}", "Switch to dark": "Switch to dark", "Switch to light": "Switch to light", "Archived": "Archived", "Save draft": "Save draft", "Done editing": "Done editing", "Restore version": "Restore version", "No history yet": "No history yet", "Source": "Source", "Imported from {{ source }}": "Imported from {{ source }}", "Stats": "Stats", "{{ count }} minute read": "{{ count }} minute read", "{{ count }} minute read_plural": "{{ count }} minute read", "{{ count }} words": "{{ count }} word", "{{ count }} words_plural": "{{ count }} words", "{{ count }} characters": "{{ count }} character", "{{ count }} characters_plural": "{{ count }} characters", "{{ number }} emoji": "{{ number }} emoji", "No text selected": "No text selected", "{{ count }} words selected": "{{ count }} word selected", "{{ count }} words selected_plural": "{{ count }} words selected", "{{ count }} characters selected": "{{ count }} character selected", "{{ count }} characters selected_plural": "{{ count }} characters selected", "Contributors": "Contributors", "Created": "Created", "Creator": "Creator", "Last edited": "Last edited", "Previously edited": "Previously edited", "No one else has viewed yet": "No one else has viewed yet", "Viewed {{ count }} times by {{ teamMembers }} people": "Viewed {{ count }} time by {{ teamMembers }} people", "Viewed {{ count }} times by {{ teamMembers }} people_plural": "Viewed {{ count }} times by {{ teamMembers }} people", "Viewer insights are disabled.": "Viewer insights are disabled.", "Sorry, the last change could not be persisted – please reload the page": "Sorry, the last change could not be persisted – please reload the page", "{{ count }} days": "{{ count }} day", "{{ count }} days_plural": "{{ count }} days", "This template will be permanently deleted in <2></2> unless restored.": "This template will be permanently deleted in <2></2> unless restored.", "This document will be permanently deleted in <2></2> unless restored.": "This document will be permanently deleted in <2></2> unless restored.", "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents": "Highlight some text and use the <1></1> control to add placeholders that can be filled out when creating new documents", "You’re editing a template": "You’re editing a template", "Deleted by {{userName}}": "Deleted by {{userName}}", "Observing {{ userName }}": "Observing {{ userName }}", "Backlinks": "Backlinks", "Close": "Close", "This document is large which may affect performance": "This document is large which may affect performance", "{{ teamName }} is using {{ appName }} to share documents, please login to continue.": "{{ teamName }} is using {{ appName }} to share documents, please login to continue.", "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?": "Are you sure you want to delete the <em>{{ documentTitle }}</em> template?", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>.", "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested document</em>._plural": "Are you sure about that? Deleting the <em>{{ documentTitle }}</em> document will delete all of its history and <em>{{ any }} nested documents</em>.", "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.": "If you’d like the option of referencing or restoring the {{noun}} in the future, consider archiving it instead.", "Select a location to move": "Select a location to move", "Document moved": "Document moved", "Couldn’t move the document, try again?": "Couldn’t move the document, try again?", "Move to <em>{{ location }}</em>": "Move to <em>{{ location }}</em>", "Couldn’t create the document, try again?": "Couldn’t create the document, try again?", "Document permanently deleted": "Document permanently deleted", "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete the <em>{{ documentTitle }}</em> document? This action is immediate and cannot be undone.", "Select a location to publish": "Select a location to publish", "Document published": "Document published", "Couldn’t publish the document, try again?": "Couldn’t publish the document, try again?", "Publish in <em>{{ location }}</em>": "Publish in <em>{{ location }}</em>", "Search documents": "Search documents", "No documents found for your filters.": "No documents found for your filters.", "You’ve not got any drafts at the moment.": "You’ve not got any drafts at the moment.", "Payment Required": "Payment Required", "No access to this doc": "No access to this doc", "It doesn’t look like you have permission to access this document.": "It doesn’t look like you have permission to access this document.", "Please request access from the document owner.": "Please request access from the document owner.", "Not found": "Not found", "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.": "The page you’re looking for cannot be found. It might have been deleted or the link is incorrect.", "Offline": "Offline", "We were unable to load the document while offline.": "We were unable to load the document while offline.", "Your account has been suspended": "Your account has been suspended", "Warning Sign": "Warning Sign", "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.": "A workspace admin (<em>{{ suspendedContactEmail }}</em>) has suspended your account. To re-activate your account, please reach out to them directly.", "Created by me": "Created by me", "Weird, this shouldn’t ever be empty": "Weird, this shouldn’t ever be empty", "You haven’t created any documents yet": "You haven’t created any documents yet", "Documents you’ve recently viewed will be here for easy access": "Documents you’ve recently viewed will be here for easy access", "We sent out your invites!": "We sent out your invites!", "Those email addresses are already invited": "Those email addresses are already invited", "Sorry, you can only send {{MAX_INVITES}} invites at a time": "Sorry, you can only send {{MAX_INVITES}} invites at a time", "Invited {{roleName}} will receive access to": "Invited {{roleName}} will receive access to", "{{collectionCount}} collections": "{{collectionCount}} collections", "Admin": "Admin", "Can manage all workspace settings": "Can manage all workspace settings", "Can create, edit, and delete documents": "Can create, edit, and delete documents", "Can view and comment": "Can view and comment", "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.": "Invite people to join your workspace. They can sign in with {{signinMethods}} or use their email address.", "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.": "Invite members to join your workspace. They will need to sign in with {{signinMethods}}.", "As an admin you can also <2>enable email sign-in</2>.": "As an admin you can also <2>enable email sign-in</2>.", "Invite as": "Invite as", "Role": "Role", "Email": "Email", "Add another": "Add another", "Inviting": "Inviting", "Send Invites": "Send Invites", "Open command menu": "Open command menu", "Forward": "Forward", "Edit current document": "Edit current document", "Move current document": "Move current document", "Open document history": "Open document history", "Jump to search": "Jump to search", "Jump to home": "Jump to home", "Focus search input": "Focus search input", "Open this guide": "Open this guide", "Enter": "Enter", "Publish document and exit": "Publish document and exit", "Save document": "Save document", "Cancel editing": "Cancel editing", "Collaboration": "Collaboration", "Formatting": "Formatting", "Paragraph": "Paragraph", "Large header": "Large header", "Medium header": "Medium header", "Small header": "Small header", "Underline": "Underline", "Undo": "Undo", "Redo": "Redo", "Move block up": "Move block up", "Move block down": "Move block down", "Lists": "Lists", "Toggle task list item": "Toggle task list item", "Tab": "Tab", "Indent list item": "Indent list item", "Outdent list item": "Outdent list item", "Move list item up": "Move list item up", "Move list item down": "Move list item down", "Tables": "Tables", "Insert row": "Insert row", "Next cell": "Next cell", "Previous cell": "Previous cell", "Space": "Space", "Numbered list": "Numbered list", "Blockquote": "Blockquote", "Horizontal divider": "Horizontal divider", "LaTeX block": "LaTeX block", "Inline code": "Inline code", "Inline LaTeX": "Inline LaTeX", "Triggers": "Triggers", "Mention users and more": "Mention users and more", "Emoji": "<PERSON><PERSON><PERSON>", "Insert block": "Insert block", "Sign In": "Sign In", "Continue with Email": "Continue with <PERSON>ail", "Continue with {{ authProviderName }}": "Continue with {{ authProviderName }}", "Back to home": "Back to home", "The workspace could not be found": "The workspace could not be found", "To continue, enter your workspace’s subdomain.": "To continue, enter your workspace’s subdomain.", "subdomain": "subdomain", "Continue": "Continue", "The domain associated with your email address has not been allowed for this workspace.": "The domain associated with your email address has not been allowed for this workspace.", "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.": "Unable to sign-in. Please navigate to your workspace's custom URL, then try to sign-in again.<1></1>If you were invited to a workspace, you will find a link to it in the invite email.", "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.": "Sorry, a new account cannot be created with a personal Gmail address.<1></1>Please use a Google Workspaces account instead.", "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.": "The workspace associated with your user is scheduled for deletion and cannot be accessed at this time.", "The workspace you authenticated with is not authorized on this installation. Try another?": "The workspace you authenticated with is not authorized on this installation. Try another?", "We could not read the user info supplied by your identity provider.": "We could not read the user info supplied by your identity provider.", "Your account uses email sign-in, please sign-in with email to continue.": "Your account uses email sign-in, please sign-in with email to continue.", "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.": "An email sign-in link was recently sent, please check your inbox or try again in a few minutes.", "Authentication failed – we were unable to sign you in at this time. Please try again.": "Authentication failed – we were unable to sign you in at this time. Please try again.", "Authentication failed – you do not have permission to access this workspace.": "Authentication failed – you do not have permission to access this workspace.", "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.": "Sorry, it looks like that sign-in link is no longer valid, please try requesting another.", "Your account has been suspended. To re-activate your account, please contact a workspace admin.": "Your account has been suspended. To re-activate your account, please contact a workspace admin.", "This workspace has been suspended. Please contact support to restore access.": "This workspace has been suspended. Please contact support to restore access.", "Authentication failed – this login method was disabled by a workspace admin.": "Authentication failed – this login method was disabled by a workspace admin.", "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.": "The workspace you are trying to join requires an invite before you can create an account.<1></1>Please request an invite from your workspace admin and try again.", "Sorry, an unknown error occurred.": "Sorry, an unknown error occurred.", "Choose a workspace": "Choose a workspace", "Choose an {{ appName }} workspace or login to continue connecting this app": "Choose an {{ appName }} workspace or login to continue connecting this app", "Create workspace": "Create workspace", "Setup your workspace by providing a name and details for admin login. You can change these later.": "Setup your workspace by providing a name and details for admin login. You can change these later.", "Workspace name": "Workspace name", "Admin name": "Admin name", "Admin email": "Admin email", "Login": "<PERSON><PERSON>", "Error": "Error", "Failed to load configuration.": "Failed to load configuration.", "Check the network requests and server logs for full details of the error.": "Check the network requests and server logs for full details of the error.", "Custom domain setup": "Custom domain setup", "Almost there": "Almost there", "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.": "Your custom domain is successfully pointing at Outline. To complete the setup process please contact support.", "Choose workspace": "Choose workspace", "This login method requires choosing your workspace to continue": "This login method requires choosing your workspace to continue", "Check your email": "Check your email", "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.": "A magic sign-in link has been sent to the email <em>{{ emailLinkSentTo }}</em> if an account exists.", "Back to login": "Back to login", "Get started by choosing a sign-in method for your new workspace below…": "Get started by choosing a sign-in method for your new workspace below…", "Login to {{ authProviderName }}": "Login to {{ authProviderName }}", "You signed in with {{ authProviderName }} last time.": "You signed in with {{ authProviderName }} last time.", "Or": "Or", "Already have an account? Go to <1>login</1>.": "Already have an account? Go to <1>login</1>.", "An error occurred": "An error occurred", "The OAuth client could not be found, please check the provided client ID": "The OAuth client could not be found, please check the provided client ID", "The OAuth client could not be loaded, please check the redirect URI is valid": "The OAuth client could not be loaded, please check the redirect URI is valid", "Required OAuth parameters are missing": "Required OAuth parameters are missing", "Authorize": "Authorize", "{{ appName }} wants to access {{ teamName }}": "{{ appName }} wants to access {{ teamName }}", "By <em>{{ developerName }}</em>": "By <em>{{ developerName }}</em>", "{{ appName }} will be able to access your account and perform the following actions": "{{ appName }} will be able to access your account and perform the following actions", "read": "read", "write": "write", "read and write": "read and write", "API keys": "API keys", "attachments": "attachments", "collections": "collections", "comments": "comments", "documents": "documents", "events": "events", "groups": "groups", "integrations": "integrations", "notifications": "notifications", "reactions": "reactions", "pins": "pins", "shares": "shares", "users": "users", "teams": "teams", "workspace": "workspace", "Read all data": "Read all data", "Write all data": "Write all data", "Any collection": "Any collection", "All time": "All time", "Past day": "Past day", "Past week": "Past week", "Past month": "Past month", "Past year": "Past year", "Any time": "Any time", "Remove document filter": "Remove document filter", "Any status": "Any status", "Remove search": "Remove search", "Any author": "Any author", "Search titles only": "Search titles only", "Something went wrong": "Something went wrong", "Please try again or contact support if the problem persists": "Please try again or contact support if the problem persists", "No documents found for your search filters.": "No documents found for your search filters.", "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.": "Create personal API keys to authenticate with the API and programatically control\n      your workspace's data. For more details see the <em>developer documentation</em>.", "API keys have been disabled by an admin for your account": "API keys have been disabled by an admin for your account", "Application access": "Application access", "Manage which third-party and internal applications have been granted access to your {{ appName }} account.": "Manage which third-party and internal applications have been granted access to your {{ appName }} account.", "API": "API", "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.": "API keys can be used to authenticate with the API and programatically control\n          your workspace's data. For more details see the <em>developer documentation</em>.", "Application published": "Application published", "Application updated": "Application updated", "Client secret rotated": "Client secret rotated", "Rotate secret": "Rotate secret", "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.": "Rotating the client secret will invalidate the current secret. Make sure to update any applications using these credentials.", "Displayed to users when authorizing": "Displayed to users when authorizing", "Developer information shown to users when authorizing": "Developer information shown to users when authorizing", "Developer name": "Developer name", "Developer URL": "Developer URL", "Allow users from other workspaces to authorize this app": "Allow users from other workspaces to authorize this app", "Credentials": "Credentials", "OAuth client ID": "OAuth client ID", "The public identifier for this app": "The public identifier for this app", "OAuth client secret": "OAuth client secret", "Store this value securely, do not expose it publicly": "Store this value securely, do not expose it publicly", "Where users are redirected after authorizing this app": "Where users are redirected after authorizing this app", "Authorization URL": "Authorization URL", "Where users are redirected to authorize this app": "Where users are redirected to authorize this app", "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.": "Applications allow you to build internal or public integrations with Outline and provide secure access via OAuth. For more details see the <em>developer documentation</em>.", "by {{ name }}": "by {{ name }}", "Last used": "Last used", "No expiry": "No expiry", "Restricted scope": "Restricted scope", "API key copied to clipboard": "API key copied to clipboard", "Copied": "<PERSON>pied", "Are you sure you want to revoke the {{ tokenName }} token?": "Are you sure you want to revoke the {{ tokenName }} token?", "Disconnect integration": "Disconnect integration", "Connected": "Connected", "Disconnect": "Disconnect", "Disconnecting": "Disconnecting", "Allowed domains": "Allowed domains", "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.": "The domains which should be allowed to create new accounts using SSO. Changing this setting does not affect existing user accounts.", "Remove domain": "Remove domain", "Add a domain": "Add a domain", "Save changes": "Save changes", "Please choose a single file to import": "Please choose a single file to import", "Your import is being processed, you can safely leave this page": "Your import is being processed, you can safely leave this page", "File not supported – please upload a valid ZIP file": "File not supported – please upload a valid ZIP file", "Set the default permission level for collections created from the import": "Set the default permission level for collections created from the import", "Uploading": "Uploading", "Start import": "Start import", "Processing": "Processing", "Expired": "Expired", "Completed": "Completed", "Failed": "Failed", "All collections": "All collections", "Import deleted": "Import deleted", "Export deleted": "Export deleted", "Are you sure you want to delete this import?": "Are you sure you want to delete this import?", "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.": "Deleting this import will also delete all collections and documents that were created from it. This cannot be undone.", "Check server logs for more details.": "Check server logs for more details.", "{{userName}} requested": "{{userName}} requested", "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.": "Groups are for organizing your team. They work best when centered around a function or a responsibility — Support or Engineering for example.", "You’ll be able to add people to the group next.": "You’ll be able to add people to the group next.", "You can edit the name of this group at any time, however doing so too often might confuse your team mates.": "You can edit the name of this group at any time, however doing so too often might confuse your team mates.", "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.": "Are you sure about that? Deleting the <em>{{groupName}}</em> group will cause its members to lose access to collections and documents that it is associated with.", "Add people to {{groupName}}": "Add people to {{groupName}}", "{{userName}} was removed from the group": "{{userName}} was removed from the group", "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.": "Add and remove members to the <em>{{groupName}}</em> group. Members of the group will have access to any collections this group has been added to.", "Add people": "Add people", "Listing members of the <em>{{groupName}}</em> group.": "Listing members of the <em>{{groupName}}</em> group.", "This group has no members.": "This group has no members.", "{{userName}} was added to the group": "{{userName}} was added to the group", "Could not add user": "Could not add user", "Add members below to give them access to the group. Need to add someone who’s not yet a member?": "Add members below to give them access to the group. Need to add someone who’s not yet a member?", "Invite them to {{teamName}}": "Invite them to {{teamName}}", "Ask an admin to invite them first": "Ask an admin to invite them first", "Search by name": "Search by name", "Search people": "Search people", "No people matching your search": "No people matching your search", "No people left to add": "No people left to add", "Date created": "Date created", "Crop Image": "Crop Image", "Crop image": "Crop image", "How does this work?": "How does this work?", "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from the JSON option in another instance. In {{ appName }}, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload": "Drag and drop the zip file from the JSON export option in {{appName}}, or click to upload", "Canceled": "Canceled", "Import canceled": "Import canceled", "Are you sure you want to cancel this import?": "Are you sure you want to cancel this import?", "Canceling": "Canceling", "Canceling this import will discard any progress made. This cannot be undone.": "Canceling this import will discard any progress made. This cannot be undone.", "{{ count }} document imported": "{{ count }} document imported", "{{ count }} document imported_plural": "{{ count }} documents imported", "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.": "You can import a zip file that was previously exported from an Outline installation – collections, documents, and images will be imported. In Outline, open <em>Export</em> in the Settings sidebar and click on <em>Export Data</em>.", "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload": "Drag and drop the zip file from the Markdown export option in {{appName}}, or click to upload", "Configure": "Configure", "Connect": "Connect", "Last active": "Last active", "Guest": "Guest", "Never used": "Never used", "Are you sure you want to delete the {{ appName }} application? This cannot be undone.": "Are you sure you want to delete the {{ appName }} application? This cannot be undone.", "Shared by": "Shared by", "Date shared": "Date shared", "Last accessed": "Last accessed", "Domain": "Domain", "Views": "Views", "All roles": "All roles", "Admins": "Admins", "Editors": "Editors", "All status": "All status", "Active": "Active", "Left": "Left", "Right": "Right", "Settings saved": "Setting<PERSON> saved", "Logo updated": "Logo updated", "Unable to upload new logo": "Unable to upload new logo", "Delete workspace": "Delete workspace", "These settings affect the way that your workspace appears to everyone on the team.": "These settings affect the way that your workspace appears to everyone on the team.", "Display": "Display", "The logo is displayed at the top left of the application.": "The logo is displayed at the top left of the application.", "The workspace name, usually the same as your company name.": "The workspace name, usually the same as your company name.", "Description": "Description", "A short description of your workspace.": "A short description of your workspace.", "Theme": "Theme", "Customize the interface look and feel.": "Customize the interface look and feel.", "Reset theme": "Reset theme", "Accent color": "Accent color", "Accent text color": "Accent text color", "Public branding": "Public branding", "Show your workspace logo, description, and branding on publicly shared pages.": "Show your workspace logo, description, and branding on publicly shared pages.", "Table of contents position": "Table of contents position", "The side to display the table of contents in relation to the main content.": "The side to display the table of contents in relation to the main content.", "Behavior": "Behavior", "Subdomain": "Subdomain", "Your workspace will be accessible at": "Your workspace will be accessible at", "Choose a subdomain to enable a login page just for your team.": "Choose a subdomain to enable a login page just for your team.", "This is the screen that workspace members will first see when they sign in.": "This is the screen that workspace members will first see when they sign in.", "Danger": "Danger", "You can delete this entire workspace including collections, documents, and users.": "You can delete this entire workspace including collections, documents, and users.", "Export data": "Export data", "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.": "A full export might take some time, consider exporting a single document or collection. You may leave this page once the export has started – if you have notifications enabled, we will email a link to <em>{{ userEmail }}</em> when it’s complete.", "Recent exports": "Recent exports", "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.": "Manage optional and beta features. Changing these settings will affect the experience for all members of the workspace.", "Separate editing": "Separate editing", "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.": "When enabled documents have a separate editing mode by default instead of being always editable. This setting can be overridden by user preferences.", "When enabled team members can add comments to documents.": "When enabled team members can add comments to documents.", "Create a group": "Create a group", "Could not load groups": "Could not load groups", "New group": "New group", "Groups can be used to organize and manage the people on your team.": "Groups can be used to organize and manage the people on your team.", "No groups have been created yet": "No groups have been created yet", "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)": "Import a zip file of Markdown documents (exported from version 0.67.0 or earlier)", "Import data": "Import data", "Import a JSON data file exported from another {{ appName }} instance": "Import a JSON data file exported from another {{ appName }} instance", "Import pages from a Confluence instance": "Import pages from a Confluence instance", "Enterprise": "Enterprise", "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.": "Quickly transfer your existing documents, pages, and files from other tools and services into {{appName}}. You can also drag and drop any HTML, Markdown, and text documents directly into Collections in the app.", "Recent imports": "Recent imports", "Configure a variety of integrations with third-party services.": "Configure a variety of integrations with third-party services.", "Could not load members": "Could not load members", "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.": "Everyone that has signed into {{appName}} is listed here. It’s possible that there are other users who have access through {{signinMethods}} but haven’t signed in yet.", "Receive a notification whenever a new document is published": "Receive a notification whenever a new document is published", "Document updated": "Document updated", "Receive a notification when a document you are subscribed to is edited": "Receive a notification when a document you are subscribed to is edited", "Comment posted": "Comment posted", "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment": "Receive a notification when a document you are subscribed to or a thread you participated in receives a comment", "Mentioned": "Mentioned", "Receive a notification when someone mentions you in a document or comment": "Receive a notification when someone mentions you in a document or comment", "Receive a notification when a comment thread you were involved in is resolved": "Receive a notification when a comment thread you were involved in is resolved", "Collection created": "Collection created", "Receive a notification whenever a new collection is created": "Receive a notification whenever a new collection is created", "Invite accepted": "<PERSON><PERSON><PERSON> accepted", "Receive a notification when someone you invited creates an account": "Receive a notification when someone you invited creates an account", "Invited to document": "Invited to document", "Receive a notification when a document is shared with you": "Receive a notification when a document is shared with you", "Invited to collection": "Invited to collection", "Receive a notification when you are given access to a collection": "Receive a notification when you are given access to a collection", "Export completed": "Export completed", "Receive a notification when an export you requested has been completed": "Receive a notification when an export you requested has been completed", "Getting started": "Getting started", "Tips on getting started with features and functionality": "Tips on getting started with features and functionality", "New features": "New features", "Receive an email when new features of note are added": "Receive an email when new features of note are added", "Notifications saved": "Notifications saved", "Unsubscription successful. Your notification settings were updated": "Unsubscription successful. Your notification settings were updated", "Manage when and where you receive email notifications.": "Manage when and where you receive email notifications.", "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.": "The email integration is currently disabled. Please set the associated environment variables and restart the server to enable notifications.", "Preferences saved": "Preferences saved", "Delete account": "Delete account", "Manage settings that affect your personal experience.": "Manage settings that affect your personal experience.", "Language": "Language", "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.": "Choose the interface language. Community translations are accepted though our <2>translation portal</2>.", "Choose your preferred interface color scheme.": "Choose your preferred interface color scheme.", "Use pointer cursor": "Use pointer cursor", "Show a hand cursor when hovering over interactive elements.": "Show a hand cursor when hovering over interactive elements.", "Show line numbers": "Show line numbers", "Show line numbers on code blocks in documents.": "Show line numbers on code blocks in documents.", "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.": "When enabled, documents have a separate editing mode. When disabled, documents are always editable when you have permission.", "Remember previous location": "Remember previous location", "Automatically return to the document you were last viewing when the app is re-opened.": "Automatically return to the document you were last viewing when the app is re-opened.", "Smart text replacements": "Smart text replacements", "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.": "Auto-format text by replacing shortcuts with symbols, dashes, smart quotes, and other typographical elements.", "You may delete your account at any time, note that this is unrecoverable": "You may delete your account at any time, note that this is unrecoverable", "Profile saved": "Profile saved", "Profile picture updated": "Profile picture updated", "Unable to upload new profile picture": "Unable to upload new profile picture", "Manage how you appear to other members of the workspace.": "Manage how you appear to other members of the workspace.", "Photo": "Photo", "Choose a photo or image to represent yourself.": "Choose a photo or image to represent yourself.", "This could be your real name, or a nickname — however you’d like people to refer to you.": "This could be your real name, or a nickname — however you’d like people to refer to you.", "Email address": "Email address", "Are you sure you want to require invites?": "Are you sure you want to require invites?", "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.": "New users will first need to be invited to create an account. <em>Default role</em> and <em>Allowed domains</em> will no longer apply.", "Settings that impact the access, security, and content of your workspace.": "Settings that impact the access, security, and content of your workspace.", "Allow members to sign-in with {{ authProvider }}": "Allow members to sign-in with {{ authProvider }}", "Disabled": "Disabled", "Allow members to sign-in using their email address": "Allow members to sign-in using their email address", "The server must have SMTP configured to enable this setting": "The server must have SMTP configured to enable this setting", "Access": "Access", "Allow users to send invites": "Allow users to send invites", "Allow editors to invite other people to the workspace": "Allow editors to invite other people to the workspace", "Require invites": "Require invites", "Require members to be invited to the workspace before they can create an account using SSO.": "Require members to be invited to the workspace before they can create an account using SSO.", "Default role": "Default role", "The default user role for new accounts. Changing this setting does not affect existing user accounts.": "The default user role for new accounts. Changing this setting does not affect existing user accounts.", "When enabled, documents can be shared publicly on the internet by any member of the workspace": "When enabled, documents can be shared publicly on the internet by any member of the workspace", "Viewer document exports": "Viewer document exports", "When enabled, viewers can see download options for documents": "When enabled, viewers can see download options for documents", "Users can delete account": "Users can delete account", "When enabled, users can delete their own account from the workspace": "When enabled, users can delete their own account from the workspace", "Rich service embeds": "Rich service embeds", "Links to supported services are shown as rich embeds within your documents": "Links to supported services are shown as rich embeds within your documents", "Collection creation": "Collection creation", "Allow editors to create new collections within the workspace": "Allow editors to create new collections within the workspace", "Workspace creation": "Workspace creation", "Allow editors to create new workspaces": "Allow editors to create new workspaces", "Could not load shares": "Could not load shares", "Sharing is currently disabled.": "<PERSON>hari<PERSON> is currently disabled.", "You can globally enable and disable public document sharing in the <em>security settings</em>.": "You can globally enable and disable public document sharing in the <em>security settings</em>.", "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.": "Documents that have been shared are listed below. Anyone that has the public link can access a read-only version of the document until the link has been revoked.", "You can create templates to help your team create consistent and accurate documentation.": "You can create templates to help your team create consistent and accurate documentation.", "Alphabetical": "Alphabetical", "There are no templates just yet.": "There are no templates just yet.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy this workspace.", "Confirmation code": "Confirmation code", "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.": "Deleting the <1>{{workspaceName}}</1> workspace will destroy all collections, documents, users, and associated data. You will be immediately logged out of {{appName}}.", "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.": "Please note that workspaces are completely separated. They can have a different domain, settings, users, and billing.", "You are creating a new workspace using your current account — <em>{{email}}</em>": "You are creating a new workspace using your current account — <em>{{email}}</em>", "To create a workspace under another email please sign up from the homepage": "To create a workspace under another email please sign up from the homepage", "Trash emptied": "Trash emptied", "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.": "Are you sure you want to permanently delete all the documents in Trash? This action is immediate and cannot be undone.", "Recently deleted": "Recently deleted", "Trash is empty at the moment.": "Trash is empty at the moment.", "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.": "A confirmation code has been sent to your email address, please enter the code below to permanently destroy your account.", "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.": "Are you sure? Deleting your account will destroy identifying data associated with your user and cannot be undone. You will be immediately logged out of {{appName}} and all your API tokens will be revoked.", "Delete my account": "Delete my account", "Today": "Today", "Yesterday": "Yesterday", "Last week": "Last week", "This month": "This month", "Last month": "Last month", "This year": "This year", "Expired yesterday": "Expired yesterday", "Expired {{ date }}": "Expired {{ date }}", "Expires today": "Expires today", "Expires tomorrow": "Expires tomorrow", "Expires {{ date }}": "Expires {{ date }}", "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in GitHub to connect {{appName}} to your workspace. Try again?", "Something went wrong while authenticating your request. Please try logging in again.": "Something went wrong while authenticating your request. Please try logging in again.", "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.": "The owner of GitHub account has been requested to install the {{githubAppName}} GitHub app. Once approved, previews will be shown for respective links.", "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.": "Enable previews of GitHub issues and pull requests in documents by connecting a GitHub organization or specific repositories to {appName}.", "Enabled by {{integrationCreatedBy}}": "Enabled by {{integrationCreated<PERSON>y}}", "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?": "Disconnecting will prevent previewing GitHub links from this organization in documents. Are you sure?", "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The GitHub integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Google Analytics": "Google Analytics", "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.": "Add a Google Analytics 4 measurement ID to send document views and analytics from the workspace to your own Google Analytics account.", "Measurement ID": "Measurement ID", "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.": "Create a \"Web\" stream in your Google Analytics admin dashboard and copy the measurement ID from the generated code snippet to install.", "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Linear to connect {{appName}} to your workspace. Try again?", "Something went wrong while processing your request. Please try again.": "Something went wrong while processing your request. Please try again.", "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.": "Enable previews of Linear issues in documents by connecting a Linear workspace to {appName}.", "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?": "Disconnecting will prevent previewing Linear links from this workspace in documents. Are you sure?", "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.": "The Linear integration is currently disabled. Please set the associated environment variables and restart the server to enable the integration.", "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.": "Configure a Matomo installation to send views and analytics from the workspace to your own Matomo instance.", "Instance URL": "Instance URL", "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/": "The URL of your Matomo instance. If you are using Matomo Cloud it will end in matomo.cloud/", "Site ID": "Site ID", "An ID that uniquely identifies the website in your Matomo instance.": "An ID that uniquely identifies the website in your Matomo instance.", "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?": "Whoops, you need to accept the permissions in Notion to connect {{ appName }} to your workspace. Try again?", "Import pages from Notion": "Import pages from Notion", "Add to Slack": "Add to Slack", "document published": "document published", "document updated": "document updated", "Posting to the <em>{{ channelName }}</em> channel on": "Posting to the <em>{{ channelName }}</em> channel on", "These events should be posted to Slack": "These events should be posted to Slack", "This will prevent any future updates from being posted to this Slack channel. Are you sure?": "This will prevent any future updates from being posted to this Slack channel. Are you sure?", "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?": "Whoops, you need to accept the permissions in Slack to connect {{appName}} to your workspace. Try again?", "Personal account": "Personal account", "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.": "Link your {{appName}} account to Slack to enable searching and previewing the documents you have access to, directly within chat.", "Disconnecting your personal account will prevent searching for documents from Slack. Are you sure?": "Disconnecting your personal account will prevent searching for documents from S<PERSON>ck. Are you sure?", "Slash command": "Slash command", "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.": "Get rich previews of {{ appName }} links shared in Slack and use the <em>{{ command }}</em> slash command to search for documents without leaving your chat.", "This will remove the Outline slash command from your Slack workspace. Are you sure?": "This will remove the Outline slash command from your Slack workspace. Are you sure?", "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.": "Connect {{appName}} collections to Slack channels. Messages will be automatically posted to Slack when documents are published or updated.", "Comment by {{ author }} on \"{{ title }}\"": "Comment by {{ author }} on \"{{ title }}\"", "How to use {{ command }}": "How to use {{ command }}", "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.": "To search your workspace use {{ command }}. \nType {{ command2 }} help to display this help text.", "Post to Channel": "Post to Channel", "This is what we found for \"{{ term }}\"": "This is what we found for \"{{ term }}\"", "No results for \"{{ term }}\"": "No results for \"{{ term }}\"", "It looks like you haven’t linked your {{ appName }} account to Slack yet": "It looks like you haven’t linked your {{ appName }} account to Slack yet", "Link your account": "Link your account", "Link your account in {{ appName }} settings to search from Slack": "Link your account in {{ appName }} settings to search from Slack", "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.": "Configure a Umami installation to send views and analytics from the workspace to your own Umami instance.", "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}": "The URL of your Umami instance. If you are using Umami Cloud it will begin with {{ url }}", "Script name": "Script name", "The name of the script file that Umami uses to track analytics.": "The name of the script file that <PERSON><PERSON> uses to track analytics.", "An ID that uniquely identifies the website in your Umami instance.": "An ID that uniquely identifies the website in your Umami instance.", "Are you sure you want to delete the {{ name }} webhook?": "Are you sure you want to delete the {{ name }} webhook?", "Webhook updated": "Webhook updated", "Update": "Update", "Updating": "Updating", "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.": "Provide a descriptive name for this webhook and the URL we should send a POST request to when matching events are created.", "A memorable identifer": "A memorable identifer", "URL": "URL", "Signing secret": "Signing secret", "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.": "Subscribe to all events, groups, or individual events. We recommend only subscribing to the minimum amount of events that your application needs to function.", "All events": "All events", "All {{ groupName }} events": "All {{ groupName }} events", "Delete webhook": "Delete webhook", "Subscribed events": "Subscribed events", "Edit webhook": "Edit webhook", "Webhook created": "Webhook created", "Webhooks": "Webhooks", "New webhook": "New webhook", "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.": "Webhooks can be used to notify your application when events happen in {{appName}}. Events are sent as a https request with a JSON payload in near real-time.", "Inactive": "Inactive", "Create a webhook": "Create a webhook", "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.": "Zapier is a platform that allows {{appName}} to easily integrate with thousands of other business tools. Automate your workflows, sync data, and more.", "Never logged in": "Never logged in", "Online now": "Online now", "Online {{ timeAgo }}": "Online {{ timeAgo }}", "Viewed just now": "Viewed just now", "You updated {{ timeAgo }}": "You updated {{ timeAgo }}", "{{ user }} updated {{ timeAgo }}": "{{ user }} updated {{ timeAgo }}", "You created {{ timeAgo }}": "You created {{ timeAgo }}", "{{ user }} created {{ timeAgo }}": "{{ user }} created {{ timeAgo }}", "Error loading data": "Error loading data"}