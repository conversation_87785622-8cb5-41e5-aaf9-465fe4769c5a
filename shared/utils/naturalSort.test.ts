import naturalSort from "./naturalSort";

describe("#naturalSort", () => {
  it("should sort a list of objects by the given key", () => {
    const items = [
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ];
    expect(naturalSort(items, "name")).toEqual([
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ]);
  });

  it("should accept a function as the object key", () => {
    const items = [
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ];
    expect(naturalSort(items, (item) => item.name)).toEqual([
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ]);
  });

  it("should accept natural-sort options", () => {
    const items = [
      {
        name: "<PERSON>",
      },
      {
        name: "joan",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ];
    expect(
      naturalSort(items, "name", {
        direction: "desc",
        caseSensitive: true,
      })
    ).toEqual([
      {
        name: "joan",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ]);
  });

  it("should ignore non basic latin letters", () => {
    const items = [
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON>",
      },
      {
        name: "Á<PERSON>",
      },
    ];
    expect(naturalSort(items, "name")).toEqual([
      {
        name: "<PERSON>",
      },
      {
        name: "<PERSON><PERSON>",
      },
      {
        name: "<PERSON>",
      },
    ]);
  });

  it("should ignore emojis", () => {
    const items = [
      {
        title: "🍔 Document 2",
      },
      {
        title: "🐻 Document 3",
      },
      {
        title: "🙂 Document 1",
      },
    ];
    expect(naturalSort(items, "title")).toEqual([
      {
        title: "🙂 Document 1",
      },
      {
        title: "🍔 Document 2",
      },
      {
        title: "🐻 Document 3",
      },
    ]);
  });
});
