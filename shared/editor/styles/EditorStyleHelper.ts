/**
 * Class names and values used by the editor.
 */
export class EditorStyleHelper {
  // Images

  static readonly imageHandle = "image-handle";

  static readonly imageCaption = "caption";

  // Comments

  static readonly comment = "comment-marker";

  // Tables

  /** Table wrapper */
  static readonly table = "table-wrapper";

  /** Table grip (circle in top left) */
  static readonly tableGrip = "table-grip";

  /** Table row grip */
  static readonly tableGripRow = "table-grip-row";

  /** Table column grip */
  static readonly tableGripColumn = "table-grip-column";

  /** "Plus" to add column on tables */
  static readonly tableAddColumn = "table-add-column";

  /** "Plus" to add row on tables */
  static readonly tableAddRow = "table-add-row";

  /** Scrollable area of table */
  static readonly tableScrollable = "table-scrollable";

  /** Full-width table layout */
  static readonly tableFullWidth = "table-full-width";

  /** Shadow on the right side of the table */
  static readonly tableShadowRight = "table-shadow-right";

  /** Shadow on the left side of the table */
  static readonly tableShadowLeft = "table-shadow-left";

  // Global

  /** Minimum padding around editor */
  static readonly padding = 32;

  /** Table of contents width */
  static readonly tocWidth = 256;
}
