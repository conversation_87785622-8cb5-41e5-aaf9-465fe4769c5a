{"extends": ["../.eslintrc"], "parserOptions": {"project": "./tsconfig.json"}, "rules": {"@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": true}], "no-restricted-imports": ["error", {"name": "fetch-with-proxy", "message": "Use `@server/utils/fetch` instead"}, {"name": "node-fetch", "message": "Use `@server/utils/fetch` instead"}, {"name": "passport", "message": "Use the `@outlinewiki/koa-passport` package"}]}, "overrides": [{"files": ["scripts/*"], "rules": {"no-console": "off"}}], "env": {"jest": true, "node": true}}