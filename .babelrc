{"presets": [["@babel/preset-react", {"runtime": "automatic"}], "@babel/preset-env", "@babel/preset-typescript"], "plugins": ["babel-plugin-transform-typescript-metadata", ["@babel/plugin-proposal-decorators", {"legacy": true}], "@babel/plugin-transform-class-properties", ["transform-inline-environment-variables", {"include": ["SOURCE_COMMIT", "SOURCE_VERSION"]}], "tsconfig-paths-module-resolver"], "env": {"production": {"plugins": [["styled-components", {"displayName": false}]], "ignore": ["**/__mocks__", "**/*.test.ts"]}, "development": {"ignore": ["**/__mocks__", "**/*.test.ts"]}, "test": {"presets": [["@babel/preset-env", {"corejs": {"version": "3", "proposals": true}, "useBuiltIns": "usage"}]]}}}