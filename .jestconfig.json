{"workerIdleMemoryLimit": "0.75", "maxWorkers": "50%", "projects": [{"displayName": "server", "roots": ["<rootDir>/server", "<rootDir>/plugins"], "moduleNameMapper": {"^@server/(.*)$": "<rootDir>/server/$1", "^@shared/(.*)$": "<rootDir>/shared/$1", "react-medium-image-zoom": "<rootDir>/__mocks__/react-medium-image-zoom.js"}, "setupFiles": ["<rootDir>/__mocks__/console.js"], "setupFilesAfterEnv": ["<rootDir>/server/test/setup.ts"], "globalSetup": "<rootDir>/server/test/globalSetup.js", "globalTeardown": "<rootDir>/server/test/globalTeardown.js", "testEnvironment": "node"}, {"displayName": "app", "roots": ["<rootDir>/app"], "moduleNameMapper": {"^~/(.*)$": "<rootDir>/app/$1", "^@shared/(.*)$": "<rootDir>/shared/$1", "^.*[.](gif|ttf|eot|svg)$": "<rootDir>/__test__/fileMock.js", "^uuid$": "<rootDir>/node_modules/uuid/dist/index.js", "react-medium-image-zoom": "<rootDir>/__mocks__/react-medium-image-zoom.js"}, "modulePaths": ["<rootDir>/app"], "setupFiles": ["<rootDir>/__mocks__/window.js"], "setupFilesAfterEnv": ["<rootDir>/app/test/setup.ts"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost"}}, {"displayName": "shared-node", "roots": ["<rootDir>/shared"], "moduleNameMapper": {"^@server/(.*)$": "<rootDir>/server/$1", "^@shared/(.*)$": "<rootDir>/shared/$1", "react-medium-image-zoom": "<rootDir>/__mocks__/react-medium-image-zoom.js"}, "setupFiles": ["<rootDir>/__mocks__/console.js"], "setupFilesAfterEnv": ["<rootDir>/shared/test/setup.ts"], "testEnvironment": "node"}, {"displayName": "shared-jsdom", "roots": ["<rootDir>/shared"], "moduleNameMapper": {"^~/(.*)$": "<rootDir>/app/$1", "^@shared/(.*)$": "<rootDir>/shared/$1", "^.*[.](gif|ttf|eot|svg)$": "<rootDir>/__test__/fileMock.js", "^uuid$": "<rootDir>/node_modules/uuid/dist/index.js", "react-medium-image-zoom": "<rootDir>/__mocks__/react-medium-image-zoom.js"}, "setupFiles": ["<rootDir>/__mocks__/window.js"], "testEnvironment": "jsdom", "testEnvironmentOptions": {"url": "http://localhost"}}]}