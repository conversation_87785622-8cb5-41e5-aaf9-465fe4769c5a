const bcrypt = require('bcryptjs');
const { Sequelize } = require('sequelize');
require('dotenv').config();

// 使用环境变量中的数据库URL
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('DATABASE_URL 环境变量未设置');
  process.exit(1);
}

// 创建数据库连接
const sequelize = new Sequelize(databaseUrl, {
  dialect: 'postgres',
  logging: false,
});

async function setupAdminPassword() {
  try {
    console.log('正在连接数据库...');
    await sequelize.authenticate();

    // 查找团队
    const [teams] = await sequelize.query(
      'SELECT id, name FROM teams ORDER BY "createdAt" DESC LIMIT 1'
    );

    if (teams.length === 0) {
      console.error('未找到团队');
      process.exit(1);
    }

    const team = teams[0];
    console.log(`找到团队: ${team.name}`);

    // 查找或创建admin用户
    const username = 'admin';
    const password = 'admin123';
    const email = `${username}@example.com`;

    // 检查用户是否存在
    const [existingUsers] = await sequelize.query(
      'SELECT id, email, name FROM users WHERE email = ? AND "teamId" = ?',
      {
        replacements: [email, team.id]
      }
    );

    // 生成密码哈希
    const saltRounds = 12;
    const passwordHash = await bcrypt.hash(password, saltRounds);

    let userId;

    if (existingUsers.length > 0) {
      // 更新现有用户
      userId = existingUsers[0].id;
      console.log(`用户 ${email} 已存在，更新密码...`);

      await sequelize.query(
        'UPDATE users SET "passwordDigest" = ?, role = \'admin\' WHERE id = ?',
        {
          replacements: [passwordHash, userId]
        }
      );
    } else {
      // 创建新用户
      console.log(`创建新用户 ${email}...`);

      const [result] = await sequelize.query(
        `INSERT INTO users (id, email, name, role, "teamId", "passwordDigest", "jwtSecret", "createdAt", "updatedAt") 
         VALUES (gen_random_uuid(), ?, ?, 'admin', ?, ?, encode(gen_random_bytes(64), 'hex'), NOW(), NOW()) 
         RETURNING id`,
        {
          replacements: [email, '管理员', team.id, passwordHash]
        }
      );

      userId = result[0].id;
    }

    console.log('\n=== 管理员账户设置成功 ===');
    console.log(`用户名: ${username}`);
    console.log(`密码: ${password}`);
    console.log(`邮箱: ${email}`);
    console.log(`角色: admin`);
    console.log(`团队: ${team.name}`);
    console.log('\n现在可以使用用户名和密码登录了！');

  } catch (error) {
    console.error('设置管理员密码时出错:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

setupAdminPassword();
