#!/usr/bin/env node

/**
 * 创建管理员账户并生成直接登录链接的脚本
 * 使用方法: node create-admin.js <email> <name>
 * 例如: node create-admin.js <EMAIL> "Admin User"
 *
 * 功能:
 * 1. 创建或更新管理员账户
 * 2. 生成直接登录链接（1分钟有效）
 * 3. 生成长期有效的访问令牌Cookie
 */

const path = require('path');
const { Sequelize } = require('sequelize');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 导入必要的模块
const env = require('./build/server/env').default;

// 初始化数据库连接
async function initDatabase() {
  const { sequelize } = require('./build/server/storage/database');
  await sequelize.authenticate();
  return sequelize;
}

// 导入模型
const { User, Team } = require('./build/server/models');

async function createAdmin() {
  const email = process.argv[2];
  const name = process.argv[3] || 'Admin User';

  if (!email) {
    console.error('请提供邮箱地址');
    console.error('使用方法: node create-admin.js <email> <name>');
    console.error('例如: node create-admin.js <EMAIL> "Admin User"');
    process.exit(1);
  }

  try {
    console.log('正在连接数据库...');
    await initDatabase();

    // 查找现有团队
    const team = await Team.findOne({
      order: [['createdAt', 'DESC']]
    });

    if (!team) {
      console.error('未找到团队，请先通过正常流程创建一个团队');
      process.exit(1);
    }

    console.log(`找到团队: ${team.name}`);

    // 检查用户是否已存在
    let user = await User.findOne({
      where: { email, teamId: team.id }
    });

    if (user) {
      console.log(`用户 ${email} 已存在，更新为管理员角色...`);
      await user.update({
        name,
        role: 'admin',
        lastActiveAt: new Date()
      });
    } else {
      console.log(`创建新的管理员用户: ${email}`);
      user = await User.create({
        email,
        name,
        role: 'admin',
        teamId: team.id,
        lastActiveAt: new Date()
      });
    }

    // 生成登录令牌
    const loginToken = user.getJwtToken();
    const transferToken = user.getTransferToken();

    console.log('\n=== 管理员账户创建成功 ===');
    console.log(`邮箱: ${user.email}`);
    console.log(`姓名: ${user.name}`);
    console.log(`角色: ${user.role}`);
    console.log(`团队: ${team.name}`);
    console.log('\n=== 直接登录链接 ===');
    console.log(`${env.URL}/auth/redirect?token=${transferToken}`);
    console.log('\n=== 或者设置Cookie后访问 ===');
    console.log(`Cookie名称: accessToken`);
    console.log(`Cookie值: ${loginToken}`);
    console.log(`然后访问: ${env.URL}`);
    console.log('\n提示: 转移令牌只有1分钟有效期，请立即使用');

  } catch (error) {
    console.error('创建管理员账户时出错:', error.message);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
createAdmin().catch(console.error);
