# Outline 管理员登录说明

## 概述

现在Outline已经配置为使用管理员账户直接登录，而不是邮件魔法链接的方式。

## 当前配置

- ✅ **邮件认证已禁用**: 不再需要邮件验证
- ✅ **管理员账户已创建**: <EMAIL> / 管理员
- ✅ **直接登录功能**: 通过令牌直接登录

## 使用方法

### 1. 创建新的管理员账户

```bash
node create-admin.js <邮箱> <姓名>
```

例如:
```bash
node create-admin.js <EMAIL> "公司管理员"
node create-admin.js <EMAIL> "<PERSON> Smith"
```

### 2. 登录方式

脚本会生成两种登录方式：

#### 方式1: 直接登录链接（推荐）
- 点击生成的链接直接登录
- 链接有效期：1分钟
- 适合立即登录使用

#### 方式2: 手动设置Cookie
- 在浏览器开发者工具中设置Cookie
- Cookie名称: `accessToken`
- Cookie值: 脚本生成的长令牌
- 然后访问 http://localhost:3000

### 3. 管理其他用户

登录后，作为管理员您可以：
- 邀请其他用户加入团队
- 管理用户权限
- 创建和管理文档
- 配置团队设置

## 脚本说明

### create-admin.js
- 创建或更新管理员账户
- 生成直接登录链接
- 支持多个管理员账户

### disable-email-auth.js
- 禁用邮件认证功能
- 只允许管理员账户登录
- 提高安全性

## 安全建议

1. **定期更换管理员账户**: 使用不同的邮箱创建多个管理员
2. **保护登录令牌**: 不要分享生成的登录链接
3. **及时登录**: 直接登录链接只有1分钟有效期
4. **备份数据**: 定期备份PostgreSQL数据库

## 故障排除

### 如果登录链接过期
重新运行脚本生成新的登录链接：
```bash
node create-admin.js <EMAIL> "管理员"
```

### 如果需要重新启用邮件认证
修改数据库中team表的设置：
```sql
UPDATE teams SET "emailSigninEnabled" = true, "guestSignin" = true;
```

### 如果忘记管理员邮箱
查看数据库中的用户：
```bash
node -e "
const { User } = require('./build/server/models');
const { sequelize } = require('./build/server/storage/database');
sequelize.authenticate().then(() => 
  User.findAll({ where: { role: 'admin' } })
).then(users => {
  users.forEach(u => console.log(\`\${u.email} - \${u.name}\`));
  process.exit(0);
});
"
```

## 应用状态

- **前端**: http://localhost:3000 (React + Vite)
- **后端**: Node.js + Express
- **数据库**: PostgreSQL (localhost:5432/outline)
- **缓存**: Redis (localhost:6379)

现在您可以直接使用管理员账户登录，无需邮件验证！
