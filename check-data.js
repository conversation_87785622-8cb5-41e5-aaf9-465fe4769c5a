const { User, Team, AuthenticationProvider } = require('./build/server/models');
const { sequelize } = require('./build/server/database');

async function checkData() {
  try {
    await sequelize.authenticate();
    console.log('=== 检查用户数据 ===');
    const users = await User.findAll({
      attributes: ['id', 'email', 'name', 'role', 'passwordDigest'],
      include: [{
        model: Team,
        attributes: ['id', 'name', 'subdomain']
      }]
    });
    
    console.log('用户数量:', users.length);
    users.forEach(user => {
      console.log('用户:', {
        id: user.id,
        email: user.email,
        name: user.name,
        role: user.role,
        hasPassword: !!user.passwordDigest,
        passwordLength: user.passwordDigest ? user.passwordDigest.length : 0,
        team: user.team ? user.team.name : 'No team'
      });
    });
    
    console.log('\n=== 检查认证提供者 ===');
    const providers = await AuthenticationProvider.findAll({
      attributes: ['id', 'name', 'enabled'],
      include: [{
        model: Team,
        attributes: ['id', 'name']
      }]
    });
    
    console.log('认证提供者数量:', providers.length);
    providers.forEach(provider => {
      console.log('提供者:', {
        id: provider.id,
        name: provider.name,
        enabled: provider.enabled,
        team: provider.team ? provider.team.name : 'No team'
      });
    });
    
    process.exit(0);
  } catch (error) {
    console.error('错误:', error);
    process.exit(1);
  }
}

checkData();
