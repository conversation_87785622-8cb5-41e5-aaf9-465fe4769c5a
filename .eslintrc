{"parser": "@typescript-eslint/parser", "parserOptions": {"sourceType": "module", "extraFileExtensions": [".json"], "project": "./tsconfig.json", "ecmaFeatures": {"jsx": true}}, "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "plugin:import/recommended", "plugin:import/typescript", "plugin:prettier/recommended"], "plugins": ["es", "react", "@typescript-eslint", "eslint-plugin-import", "eslint-plugin-node", "eslint-plugin-react", "eslint-plugin-lodash"], "rules": {"no-restricted-imports": ["error", {"paths": [{"name": "re<PERSON><PERSON>/<PERSON>u", "importNames": ["useMenuState"], "message": "Do not use useMenuState from reakit/Menu. Use useMenuState instead."}]}], "eqeqeq": 2, "curly": 2, "no-console": "error", "arrow-body-style": ["error", "as-needed"], "spaced-comment": "error", "object-shorthand": "error", "no-mixed-operators": "off", "no-useless-escape": "off", "no-shadow": "off", "es/no-regexp-lookbehind-assertions": "error", "react/react-in-jsx-scope": "off", "react/self-closing-comp": ["error", {"component": true, "html": true}], "@typescript-eslint/no-shadow": ["warn", {"allow": ["transaction"], "hoist": "all", "ignoreTypeValueShadow": true}], "@typescript-eslint/no-require-imports": "off", "@typescript-eslint/no-explicit-any": "warn", "@typescript-eslint/no-floating-promises": "error", "@typescript-eslint/await-thenable": "error", "@typescript-eslint/no-misused-promises": ["error", {"checksVoidReturn": false}], "@typescript-eslint/no-unused-vars": ["error", {"argsIgnorePattern": "^_", "caughtErrorsIgnorePattern": "^_", "args": "after-used", "ignoreRestSiblings": true}], "padding-line-between-statements": ["error", {"blankLine": "always", "prev": "*", "next": "export"}], "lines-between-class-members": ["error", "always", {"exceptAfterSingleLine": true}], "lodash/import-scope": ["error", "method"], "import/no-named-as-default": "off", "import/no-named-as-default-member": "off", "import/newline-after-import": 2, "import/order": ["error", {"alphabetize": {"order": "asc"}, "pathGroups": [{"pattern": "@shared/**", "group": "external", "position": "after"}, {"pattern": "@server/**", "group": "external", "position": "after"}, {"pattern": "~/stores", "group": "external", "position": "after"}, {"pattern": "~/stores/**", "group": "external", "position": "after"}, {"pattern": "~/models/**", "group": "external", "position": "after"}, {"pattern": "~/scenes/**", "group": "external", "position": "after"}, {"pattern": "~/components/**", "group": "external", "position": "after"}, {"pattern": "~/**", "group": "external", "position": "after"}]}], "prettier/prettier": ["error", {"printWidth": 80, "trailingComma": "es5"}]}, "settings": {"react": {"createClass": "createReactClass", "pragma": "React", "version": "detect"}, "import/parsers": {"@typescript-eslint/parser": [".ts", ".tsx"]}, "import/resolver": {"typescript": {}}}}