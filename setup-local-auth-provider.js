const { Sequelize } = require('sequelize');
require('dotenv').config();

// 使用环境变量中的数据库URL
const databaseUrl = process.env.DATABASE_URL;

if (!databaseUrl) {
  console.error('DATABASE_URL 环境变量未设置');
  process.exit(1);
}

// 创建数据库连接
const sequelize = new Sequelize(databaseUrl, {
  dialect: 'postgres',
  logging: false,
});

async function setupLocalAuthProvider() {
  try {
    console.log('正在连接数据库...');
    await sequelize.authenticate();
    
    // 查找团队
    const [teams] = await sequelize.query(
      'SELECT id, name FROM teams ORDER BY "createdAt" DESC LIMIT 1'
    );
    
    if (teams.length === 0) {
      console.error('未找到团队');
      process.exit(1);
    }
    
    const team = teams[0];
    console.log(`找到团队: ${team.name}`);
    
    // 检查是否已存在本地认证提供者
    const [existingProviders] = await sequelize.query(
      'SELECT id, name FROM authentication_providers WHERE name = ? AND "teamId" = ?',
      {
        replacements: ['local', team.id]
      }
    );
    
    if (existingProviders.length > 0) {
      console.log('本地认证提供者已存在');
      return;
    }
    
    // 创建本地认证提供者
    console.log('创建本地认证提供者...');
    
    await sequelize.query(
      `INSERT INTO authentication_providers (id, name, enabled, "providerId", "teamId", "createdAt") 
       VALUES (gen_random_uuid(), 'local', true, 'local', ?, NOW())`,
      {
        replacements: [team.id]
      }
    );
    
    console.log('\n=== 本地认证提供者设置成功 ===');
    console.log(`团队: ${team.name}`);
    console.log('认证提供者: local');
    console.log('状态: 已启用');
    console.log('\n现在可以使用本地用户名密码登录了！');
    
  } catch (error) {
    console.error('设置本地认证提供者时出错:', error);
    process.exit(1);
  } finally {
    await sequelize.close();
  }
}

setupLocalAuthProvider();
