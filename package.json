{"name": "outline", "private": true, "license": "Business Source License 1.1", "main": "index.js", "scripts": {"clean": "<PERSON><PERSON><PERSON> build", "copy:i18n": "mkdir -p ./build/shared/i18n && cp -R ./shared/i18n/locales ./build/shared/i18n", "build:i18n": "i18next --silent '{shared,app,server,plugins}/**/*.{ts,tsx}' && yarn copy:i18n", "build:server": "node ./build.js", "build": "yarn clean && yarn vite:build && yarn build:i18n && yarn build:server", "start": "node ./build/server/index.js", "dev": "NODE_ENV=development yarn concurrently -n api,collaboration  -c \"blue,magenta\" \"node --inspect=0.0.0.0 build/server/index.js --services=cron,collaboration,websockets,admin,web,worker\"", "dev:backend": "NODE_ENV=development nodemon --exec \"yarn build:server && yarn dev\" -e js,ts,tsx --ignore *.test.ts --ignore data/ --ignore build/ --ignore app/ --ignore shared/editor --ignore server/migrations", "dev:watch": "NODE_ENV=development yarn concurrently -n backend,frontend \"yarn dev:backend\" \"yarn vite:dev\"", "lint": "eslint app server shared plugins", "lint:changed": "git diff --name-only --diff-filter=ACMRTUXB | grep -E '\\.(js|jsx|ts|tsx)$' | xargs -r yarn eslint --fix", "prepare": "husky install", "postinstall": "yarn patch-package", "install-local-ssl": "node ./server/scripts/install-local-ssl.js", "release": "node ./server/scripts/release.js", "heroku-postbuild": "yarn build && yarn db:migrate", "db:create-migration": "sequelize migration:create", "db:create": "sequelize db:create", "db:migrate": "sequelize db:migrate", "db:rollback": "sequelize db:migrate:undo", "db:reset": "sequelize db:drop && sequelize db:create && sequelize db:migrate", "upgrade": "git fetch && git pull && yarn install && yarn heroku-postbuild", "test": "TZ=UTC jest --config=.jestconfig.json --forceExit", "test:app": "TZ=UTC jest --config=.jestconfig.json --selectProjects app", "test:shared": "TZ=UTC jest --config=.jestconfig.json --selectProjects shared-node shared-jsdom", "test:server": "TZ=UTC jest --config=.jestconfig.json --selectProjects server", "vite:dev": "VITE_CJS_IGNORE_WARNING=true vite", "vite:build": "VITE_CJS_IGNORE_WARNING=true vite build", "vite:preview": "VITE_CJS_IGNORE_WARNING=true vite preview"}, "funding": {"type": "GitHub Sponsors ❤", "url": "https://github.com/sponsors/outline"}, "engines": {"node": ">= 20 <=22"}, "repository": {"type": "git", "url": "git+ssh://**************/outline/outline.git"}, "browserslist": ["> 0.25%, not dead"], "dependencies": {"@aws-sdk/client-s3": "3.840.0", "@aws-sdk/lib-storage": "3.840.0", "@aws-sdk/s3-presigned-post": "3.840.0", "@aws-sdk/s3-request-presigner": "3.840.0", "@aws-sdk/signature-v4-crt": "^3.840.0", "@babel/core": "^7.27.7", "@babel/plugin-proposal-decorators": "^7.27.1", "@babel/plugin-transform-class-properties": "^7.27.1", "@babel/plugin-transform-destructuring": "^7.27.7", "@babel/plugin-transform-regenerator": "^7.27.5", "@babel/preset-env": "^7.27.2", "@babel/preset-react": "^7.27.1", "@benrbray/prosemirror-math": "^0.2.2", "@bull-board/api": "^6.7.10", "@bull-board/koa": "^6.9.6", "@css-inline/css-inline-wasm": "^0.14.3", "@dnd-kit/core": "^6.3.1", "@dnd-kit/modifiers": "^6.0.1", "@dnd-kit/sortable": "^7.0.2", "@emoji-mart/data": "^1.2.1", "@fast-csv/parse": "^5.0.2", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-brands-svg-icons": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/react-fontawesome": "^0.2.2", "@getoutline/react-roving-tabindex": "^3.2.4", "@hocuspocus/extension-throttle": "1.1.2", "@hocuspocus/provider": "1.1.2", "@hocuspocus/server": "1.1.2", "@joplin/turndown-plugin-gfm": "^1.0.49", "@juggle/resize-observer": "^3.4.0", "@linear/sdk": "^39.0.0", "@node-oauth/oauth2-server": "^5.2.0", "@notionhq/client": "^2.3.0", "@octokit/auth-app": "^6.1.3", "@outlinewiki/koa-passport": "^4.2.1", "@outlinewiki/passport-azure-ad-oauth2": "^0.1.0", "@radix-ui/react-collapsible": "^1.1.11", "@radix-ui/react-popover": "^1.1.14", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-switch": "^1.2.5", "@radix-ui/react-tabs": "^1.1.12", "@radix-ui/react-tooltip": "^1.2.7", "@radix-ui/react-visually-hidden": "^1.2.2", "@renderlesskit/react": "^0.11.0", "@sentry/node": "^7.120.3", "@sentry/react": "^7.120.3", "@tanstack/react-table": "^8.21.3", "@tanstack/react-virtual": "^3.13.6", "@types/form-data": "^2.5.2", "@types/mailparser": "^3.4.6", "@types/sanitize-filename": "^1.6.3", "@vitejs/plugin-react": "^3.1.0", "addressparser": "^1.0.1", "async-sema": "^3.1.1", "autotrack": "^2.4.1", "babel-plugin-styled-components": "^2.1.4", "babel-plugin-transform-class-properties": "^6.24.1", "bcryptjs": "^3.0.2", "body-scroll-lock": "^4.0.0-beta.0", "bull": "^4.16.5", "chalk": "^4.1.0", "class-validator": "^0.14.2", "command-score": "^0.1.2", "compressorjs": "^1.2.1", "cookie": "^0.7.0", "copy-to-clipboard": "^3.3.3", "core-js": "^3.37.0", "crypto-js": "^4.2.0", "datadog-metrics": "^0.12.1", "date-fns": "^3.6.0", "dd-trace": "^5.40.0", "diff": "^5.2.0", "dotenv": "^16.5.0", "email-providers": "^1.14.0", "emoji-mart": "^5.6.0", "emoji-regex": "^10.4.0", "es6-error": "^4.1.1", "fast-deep-equal": "^3.1.3", "fetch-retry": "^5.0.6", "fetch-with-proxy": "^3.0.1", "form-data": "^4.0.2", "fractional-index": "^1.0.0", "framer-motion": "^4.1.17", "fs-extra": "^11.2.0", "fuzzy-search": "^3.2.1", "glob": "^8.1.0", "http-errors": "2.0.0", "https-proxy-agent": "^7.0.6", "i18next": "^22.5.1", "i18next-fs-backend": "^2.6.0", "i18next-http-backend": "^2.7.3", "invariant": "^2.2.4", "ioredis": "^5.6.0", "is-printable-key-event": "^1.0.0", "jsdom": "^22.1.0", "jsonwebtoken": "^9.0.0", "jszip": "^3.10.1", "katex": "^0.16.22", "kbar": "0.1.0-beta.41", "koa": "^2.16.1", "koa-body": "^6.0.1", "koa-compress": "^5.1.1", "koa-helmet": "^6.1.0", "koa-logger": "^3.2.1", "koa-mount": "^4.2.0", "koa-router": "7.4.0", "koa-send": "5.0.1", "koa-sslify": "5.0.1", "koa-useragent": "^4.1.0", "lodash": "^4.17.21", "mailparser": "^3.7.3", "mammoth": "^1.9.0", "markdown-it": "^14.1.0", "markdown-it-container": "^3.0.0", "markdown-it-emoji": "^3.0.0", "mermaid": "11.7.0", "mime-types": "^2.1.35", "mobx": "^4.15.4", "mobx-react": "^6.3.1", "mobx-utils": "^4.0.1", "natural-sort": "^1.0.0", "node-fetch": "2.7.0", "nodemailer": "^6.10.0", "octokit": "^3.2.2", "outline-icons": "^3.12.1", "oy-vey": "^0.12.1", "passport": "^0.7.0", "passport-google-oauth2": "^0.2.0", "passport-local": "^1.0.0", "passport-oauth2": "^1.8.0", "passport-slack-oauth2": "^1.2.0", "patch-package": "^7.0.2", "pg": "^8.15.6", "pg-tsquery": "^8.4.2", "pluralize": "^8.0.0", "png-chunks-extract": "^1.0.0", "polished": "^4.3.1", "prosemirror-codemark": "^0.4.2", "prosemirror-commands": "^1.7.1", "prosemirror-dropcursor": "^1.8.1", "prosemirror-gapcursor": "^1.3.2", "prosemirror-history": "^1.4.1", "prosemirror-inputrules": "^1.5.0", "prosemirror-keymap": "^1.2.2", "prosemirror-markdown": "^1.13.2", "prosemirror-model": "^1.25.0", "prosemirror-schema-list": "^1.5.1", "prosemirror-state": "^1.4.3", "prosemirror-tables": "^1.7.1", "prosemirror-transform": "1.10.0", "prosemirror-view": "^1.39.1", "query-string": "^7.1.3", "randomstring": "1.3.1", "rate-limiter-flexible": "^2.4.2", "react": "^17.0.2", "react-avatar-editor": "^13.0.2", "react-color": "^2.17.3", "react-day-picker": "^8.10.1", "react-dnd": "^16.0.1", "react-dnd-html5-backend": "^16.0.1", "react-dom": "^17.0.2", "react-dropzone": "^11.7.1", "react-helmet-async": "^2.0.5", "react-hook-form": "^7.54.2", "react-i18next": "^12.3.1", "react-medium-image-zoom": "5.2.14", "react-merge-refs": "^2.1.1", "react-portal": "^4.3.0", "react-router-dom": "^5.3.4", "react-virtualized-auto-sizer": "^1.0.26", "react-waypoint": "^10.3.0", "react-window": "^1.8.11", "reakit": "^1.3.11", "redlock": "^5.0.0-beta.2", "reflect-metadata": "^0.2.2", "refractor": "^3.6.0", "request-filtering-agent": "^1.1.2", "resolve-path": "^1.4.0", "rfc6902": "^5.1.2", "sanitize-filename": "^1.6.3", "scroll-into-view-if-needed": "^3.1.0", "semver": "^7.7.2", "sequelize": "^6.37.3", "sequelize-cli": "^6.6.3", "sequelize-encrypted": "^1.0.0", "sequelize-strict-attributes": "^1.0.2", "sequelize-typescript": "^2.1.6", "slug": "^5.3.0", "slugify": "^1.6.6", "socket.io": "^4.8.1", "socket.io-client": "^4.8.1", "socket.io-redis": "^6.1.1", "sonner": "^1.7.4", "stoppable": "^1.1.0", "string-replace-to-array": "^2.1.1", "styled-components": "^5.3.11", "styled-components-breakpoint": "^2.1.1", "styled-normalize": "^8.1.1", "throng": "^5.0.0", "tiny-cookie": "^2.5.1", "tmp": "^0.2.3", "turndown": "^7.2.0", "ukkonen": "^2.1.0", "umzug": "^3.8.2", "utility-types": "^3.11.0", "uuid": "^8.3.2", "validator": "13.15.0", "vaul": "^1.1.2", "vite": "^6.3.4", "vite-plugin-pwa": "^0.21.2", "winston": "^3.17.0", "ws": "^7.5.10", "y-indexeddb": "^9.0.11", "y-prosemirror": "^1.2.12", "y-protocols": "^1.0.6", "yauzl": "^2.10.0", "yjs": "^13.6.1", "zod": "^3.24.2"}, "devDependencies": {"@babel/cli": "^7.27.2", "@babel/preset-typescript": "^7.27.1", "@faker-js/faker": "^8.4.1", "@relative-ci/agent": "^4.3.0", "@testing-library/react": "^12.0.0", "@types/addressparser": "^1.0.3", "@types/body-scroll-lock": "^3.1.2", "@types/crypto-js": "^4.2.2", "@types/diff": "^5.0.9", "@types/dotenv": "^8.2.3", "@types/emoji-regex": "^9.2.2", "@types/escape-html": "^1.0.4", "@types/express-useragent": "^1.0.5", "@types/formidable": "^2.0.6", "@types/fs-extra": "^11.0.4", "@types/fuzzy-search": "^2.1.5", "@types/glob": "^8.0.1", "@types/google.analytics": "^0.0.46", "@types/invariant": "^2.2.37", "@types/jest": "^29.5.14", "@types/jsonwebtoken": "^8.5.9", "@types/katex": "^0.16.7", "@types/koa": "^2.15.0", "@types/koa-compress": "^4.0.6", "@types/koa-helmet": "^6.0.8", "@types/koa-logger": "^3.1.5", "@types/koa-mount": "^4.0.5", "@types/koa-router": "^7.4.8", "@types/koa-send": "^4.1.6", "@types/koa-sslify": "^4.0.6", "@types/koa-useragent": "^2.1.2", "@types/markdown-it": "^14.1.2", "@types/markdown-it-container": "^2.0.9", "@types/markdown-it-emoji": "^3.0.1", "@types/mime-types": "^2.1.4", "@types/natural-sort": "^0.0.24", "@types/node": "20.17.30", "@types/node-fetch": "^2.6.9", "@types/nodemailer": "^6.4.17", "@types/passport-local": "^1.0.38", "@types/passport-oauth2": "^1.4.17", "@types/pluralize": "^0.0.33", "@types/png-chunks-extract": "^1.0.2", "@types/quoted-printable": "^1.0.2", "@types/randomstring": "^1.3.0", "@types/react": "^17.0.34", "@types/react-avatar-editor": "^13.0.4", "@types/react-color": "^3.0.13", "@types/react-dom": "^17.0.11", "@types/react-helmet": "^6.1.11", "@types/react-portal": "^4.0.7", "@types/react-router-dom": "^5.3.3", "@types/react-virtualized-auto-sizer": "^1.0.4", "@types/react-window": "^1.8.8", "@types/readable-stream": "^4.0.21", "@types/redis-info": "^3.0.3", "@types/refractor": "^3.4.1", "@types/resolve-path": "^1.4.3", "@types/semver": "^7.7.0", "@types/sequelize": "^4.28.20", "@types/slug": "^5.0.7", "@types/stoppable": "^1.1.3", "@types/styled-components": "^5.1.32", "@types/throng": "^5.0.7", "@types/tmp": "^0.2.6", "@types/turndown": "^5.0.5", "@types/utf8": "^3.0.3", "@types/validator": "^13.15.0", "@types/yauzl": "^2.10.3", "@typescript-eslint/eslint-plugin": "^8.33.0", "@typescript-eslint/parser": "^8.33.0", "babel-eslint": "^10.1.0", "babel-jest": "^29.7.0", "babel-plugin-transform-inline-environment-variables": "^0.4.4", "babel-plugin-transform-typescript-metadata": "^0.3.2", "babel-plugin-tsconfig-paths-module-resolver": "^1.0.4", "browserslist-to-esbuild": "^1.2.0", "concurrently": "^8.2.2", "discord-api-types": "^0.37.119", "eslint": "^8.57.0", "eslint-config-prettier": "^8.10.0", "eslint-import-resolver-typescript": "^3.10.1", "eslint-plugin-es": "^4.1.0", "eslint-plugin-import": "^2.31.0", "eslint-plugin-jsx-a11y": "^6.10.2", "eslint-plugin-lodash": "^7.4.0", "eslint-plugin-node": "^11.1.0", "eslint-plugin-prettier": "^5.5.1", "eslint-plugin-react": "^7.37.3", "eslint-plugin-react-hooks": "^4.6.2", "husky": "^8.0.3", "i18next-parser": "^8.13.0", "jest-cli": "^29.7.0", "jest-environment-jsdom": "^29.7.0", "jest-fetch-mock": "^3.0.3", "lint-staged": "^13.3.0", "nodemon": "^3.1.10", "postinstall-postinstall": "^2.1.0", "prettier": "^3.6.2", "react-refresh": "^0.17.0", "rimraf": "^2.5.4", "rollup-plugin-webpack-stats": "^2.0.5", "terser": "^5.43.1", "typescript": "^5.8.3", "vite-plugin-static-copy": "^0.17.0", "yarn-deduplicate": "^6.0.2"}, "resolutions": {"prosemirror-transform": "1.10.0", "body-scroll-lock": "^4.0.0-beta.0", "d3": "^7.0.0", "debug": "4.3.4", "node-fetch": "^2.7.0", "js-yaml": "^3.14.1", "qs": "6.9.7", "prismjs": "1.30.0"}, "version": "0.84.0", "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}