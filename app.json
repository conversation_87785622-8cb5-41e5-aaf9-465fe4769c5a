{"name": "Outline", "description": "Open source wiki and knowledge base for growing teams", "website": "https://www.getoutline.com/", "repository": "https://github.com/outline/outline", "keywords": ["wiki", "team", "node", "markdown", "slack"], "success_url": "/", "formation": {"web": {"quantity": 1, "size": "<PERSON>bby"}}, "image": "heroku/node", "addons": [{"plan": "<PERSON><PERSON>-redis"}, {"plan": "heroku-postgresql"}], "scripts": {"postdeploy": "yarn sequelize db:migrate"}, "env": {"NODE_ENV": {"value": "production", "required": true}, "SECRET_KEY": {"description": "A 32-character secret key, generate with openssl rand -hex 32", "generator": "secret", "required": true}, "UTILS_SECRET": {"description": "A 32-character secret key, generate with openssl rand -hex 32", "generator": "secret", "required": true}, "ENABLE_UPDATES": {"value": "true", "required": true}, "URL": {"description": "https://{your app name}.herokuapp.com, or the domain you are binding to", "required": true}, "GOOGLE_CLIENT_ID": {"description": "See https://developers.google.com/identity/protocols/OAuth2 to create a new Google OAuth client. You must configure at least one of Slack or Google to control login.", "required": false}, "GOOGLE_CLIENT_SECRET": {"description": "", "required": false}, "AZURE_CLIENT_ID": {"description": "To configure Microsoft/Azure auth, you'll need to create an OAuth Client. See the guide for details on setting up your Azure App: https://wiki.generaloutline.com/share/dfa77e56-d4d2-4b51-8ff8-84ea6608faa4", "required": false}, "AZURE_CLIENT_SECRET": {"description": "", "required": false}, "AZURE_RESOURCE_APP_ID": {"description": "", "required": false}, "OIDC_CLIENT_ID": {"description": "To configure generic OIDC auth, you'll need some kind of identity provider. See documentation for whichever IdP you use to acquire the following info; Redirect URI is https://<URL>/auth/oidc.callback", "required": false}, "OIDC_CLIENT_SECRET": {"description": "", "required": false}, "OIDC_AUTH_URI": {"description": "", "required": false}, "OIDC_TOKEN_URI": {"description": "", "required": false}, "OIDC_USERINFO_URI": {"description": "", "required": false}, "OIDC_DISABLE_REDIRECT": {"description": "Prevent the app from automatically redirecting to the OIDC login page", "required": false}, "OIDC_LOGOUT_URI": {"description": "", "required": false}, "OIDC_USERNAME_CLAIM": {"description": "Specify which claims to derive user information from. Supports any valid JSON path with the JWT payload", "value": "preferred_username", "required": false}, "OIDC_DISPLAY_NAME": {"description": "Display name for OIDC authentication", "value": "OpenID Connect", "required": false}, "OIDC_SCOPES": {"description": "Space separated auth scopes.", "value": "openid profile email", "required": false}, "SLACK_CLIENT_ID": {"description": "See https://api.slack.com/apps to create a new Slack app. You must configure at least one of Slack or Google to control login.", "required": false}, "SLACK_CLIENT_SECRET": {"description": "Your Slack client secret - d2dc414f9953226bad0a356cXXXXYYYY", "required": false}, "SLACK_VERIFICATION_TOKEN": {"description": "Your Slack verification token - PLxk6OlXXXXXVj3YYYY", "required": false}, "SLACK_APP_ID": {"description": "A0XXXXXXXXX", "required": false}, "AWS_ACCESS_KEY_ID": {"description": "Needed to save file uploads. Optional for development / testing", "required": false}, "AWS_SECRET_ACCESS_KEY": {"description": "", "required": false}, "AWS_S3_UPLOAD_BUCKET_NAME": {"description": "yourbucket.example.com", "required": false}, "AWS_S3_UPLOAD_BUCKET_URL": {"description": "Live web link to your bucket. For CNAMEs, https://yourbucket.example.com", "required": false}, "AWS_S3_FORCE_PATH_STYLE": {"description": "Use path-style URL's for connecting to S3 instead of subdomain. This is useful for S3-compatible storage.", "value": "true", "required": false}, "AWS_REGION": {"value": "us-east-1", "description": "Region in which the above S3 bucket exists", "required": false}, "AWS_S3_ACL": {"value": "private", "description": "S3 canned ACL for document attachments", "required": false}, "FILE_STORAGE_UPLOAD_MAX_SIZE": {"description": "Maximum file upload size in bytes", "value": "26214400", "required": false}, "SMTP_HOST": {"description": "smtp.example.com (optional)", "required": false}, "SMTP_SERVICE": {"description": "Well-known SMTP service name for nodemailer (optional, e.g. 'gmail', 'SES')", "required": false}, "SMTP_PORT": {"description": "1234 (optional)", "required": false}, "SMTP_USERNAME": {"description": "<EMAIL> (optional)", "required": false}, "SMTP_PASSWORD": {"description": "(optional)", "required": false}, "SMTP_FROM_EMAIL": {"description": "<EMAIL> (optional)", "required": false}, "SMTP_REPLY_EMAIL": {"description": "<EMAIL> (optional)", "required": false}, "SMTP_SECURE": {"value": "true", "description": "Use a secure SMTP connection (optional)", "required": false}, "SMTP_TLS_CIPHERS": {"description": "Override SMTP cipher configuration (optional)", "required": false}, "GOOGLE_ANALYTICS_ID": {"description": "G-xxxx (optional)", "required": false}, "SENTRY_DSN": {"description": "An API key for Sentry if you wish to collect error reporting (optional)", "required": false}, "SENTRY_TUNNEL": {"description": "A sentry tunnel URL for bypassing ad blockers in the UI (optional)", "required": false}, "DEFAULT_LANGUAGE": {"value": "en_US", "description": "The default interface language. See translate.getoutline.com for a list of available language codes and their rough percentage translated.", "required": false}}}