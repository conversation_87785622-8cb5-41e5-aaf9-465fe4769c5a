#!/usr/bin/env node

/**
 * 禁用邮件认证的脚本
 * 这将关闭团队的邮件登录功能，只允许通过管理员账户登录
 */

const path = require('path');

// 设置环境变量
process.env.NODE_ENV = 'development';

// 导入必要的模块
const env = require('./build/server/env').default;

// 初始化数据库连接
async function initDatabase() {
  const { sequelize } = require('./build/server/storage/database');
  await sequelize.authenticate();
  return sequelize;
}

// 导入模型
const { Team } = require('./build/server/models');

async function disableEmailAuth() {
  try {
    console.log('正在连接数据库...');
    await initDatabase();
    
    // 查找现有团队
    const team = await Team.findOne({
      order: [['createdAt', 'DESC']]
    });

    if (!team) {
      console.error('未找到团队');
      process.exit(1);
    }

    console.log(`找到团队: ${team.name}`);

    // 禁用邮件登录
    await team.update({
      emailSigninEnabled: false,
      guestSignin: false
    });

    console.log('\n=== 邮件认证已禁用 ===');
    console.log('现在只能通过管理员账户直接登录');
    console.log('使用 node create-admin.js 创建管理员账户');

  } catch (error) {
    console.error('禁用邮件认证时出错:', error.message);
    process.exit(1);
  } finally {
    process.exit(0);
  }
}

// 运行脚本
disableEmailAuth().catch(console.error);
